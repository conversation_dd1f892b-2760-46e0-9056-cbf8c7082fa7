<?php

namespace App\Models;

class StaffLeaveRequestModel extends BaseModel
{
    protected $table = 'staff_leave_request';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'staff_id', 'leave_type_id', 'apply_date', 'leave_from', 'leave_to',
        'leave_days', 'employee_remark', 'admin_remark', 'status',
        'approved_by', 'approved_date', 'document', 'is_active'
    ];

    protected $validationRules = [
        'staff_id' => 'required|integer',
        'leave_type_id' => 'required|integer',
        'apply_date' => 'required|valid_date',
        'leave_from' => 'required|valid_date',
        'leave_to' => 'required|valid_date',
        'leave_days' => 'required|integer|greater_than[0]',
        'status' => 'permit_empty|in_list[pending,approved,disapproved]'
    ];

    protected $validationMessages = [
        'staff_id' => [
            'required' => 'Staff member is required',
            'integer' => 'Invalid staff ID'
        ],
        'leave_type_id' => [
            'required' => 'Leave type is required',
            'integer' => 'Invalid leave type'
        ],
        'apply_date' => [
            'required' => 'Application date is required',
            'valid_date' => 'Please enter a valid application date'
        ],
        'leave_from' => [
            'required' => 'Leave start date is required',
            'valid_date' => 'Please enter a valid start date'
        ],
        'leave_to' => [
            'required' => 'Leave end date is required',
            'valid_date' => 'Please enter a valid end date'
        ],
        'leave_days' => [
            'required' => 'Number of leave days is required',
            'integer' => 'Leave days must be a number',
            'greater_than' => 'Leave days must be greater than 0'
        ]
    ];

    protected $searchableColumns = ['apply_date', 'leave_from', 'leave_to', 'employee_remark'];
    protected $orderableColumns = ['id', 'apply_date', 'leave_from', 'leave_to', 'leave_days', 'status'];

    /**
     * Get leave requests with details
     */
    public function getLeaveRequestsWithDetails($filters = [])
    {
        $builder = $this->db->table($this->table);
        $builder->select('staff_leave_request.*, staff.name, staff.surname, staff.employee_id,
                         staff_leave_types.type as leave_type, staff_leave_types.max_leave,
                         department.department_name, staff_designation.designation,
                         approver.firstname as approver_firstname, approver.lastname as approver_lastname')
                ->join('staff', 'staff_leave_request.staff_id = staff.id')
                ->join('staff_leave_types', 'staff_leave_request.leave_type_id = staff_leave_types.id')
                ->join('department', 'staff.department = department.id', 'left')
                ->join('staff_designation', 'staff.designation = staff_designation.id', 'left')
                ->join('users as approver', 'staff_leave_request.approved_by = approver.id', 'left')
                ->where('staff_leave_request.is_active', 'yes');

        // Apply filters
        if (!empty($filters['staff_id'])) {
            $builder->where('staff_leave_request.staff_id', $filters['staff_id']);
        }

        if (!empty($filters['leave_type_id'])) {
            $builder->where('staff_leave_request.leave_type_id', $filters['leave_type_id']);
        }

        if (!empty($filters['status'])) {
            $builder->where('staff_leave_request.status', $filters['status']);
        }

        if (!empty($filters['department_id'])) {
            $builder->where('staff.department', $filters['department_id']);
        }

        if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
            $builder->where('staff_leave_request.leave_from >=', $filters['start_date']);
            $builder->where('staff_leave_request.leave_to <=', $filters['end_date']);
        }

        if (!empty($filters['year'])) {
            $builder->where('YEAR(staff_leave_request.leave_from)', $filters['year']);
        }

        $builder->orderBy('staff_leave_request.apply_date', 'DESC');

        return $builder->get()->getResultArray();
    }

    /**
     * Apply for leave
     */
    public function applyLeave($data)
    {
        // Validate date range
        if (strtotime($data['leave_from']) > strtotime($data['leave_to'])) {
            return [
                'success' => false,
                'message' => 'Leave end date must be after or equal to start date'
            ];
        }

        // Calculate leave days
        $startDate = new \DateTime($data['leave_from']);
        $endDate = new \DateTime($data['leave_to']);
        $interval = $startDate->diff($endDate);
        $leaveDays = $interval->days + 1; // Include both start and end dates

        // Check leave balance
        $leaveTypesModel = new StaffLeaveTypesModel();
        $balance = $leaveTypesModel->getLeaveBalance($data['staff_id'], $data['leave_type_id']);
        
        if ($balance && $leaveDays > $balance['balance']) {
            return [
                'success' => false,
                'message' => "Insufficient leave balance. Available: {$balance['balance']} days, Requested: {$leaveDays} days"
            ];
        }

        // Check for overlapping leave requests
        $overlapping = $this->checkOverlappingLeave($data['staff_id'], $data['leave_from'], $data['leave_to']);
        if ($overlapping) {
            return [
                'success' => false,
                'message' => 'You already have a leave request for overlapping dates'
            ];
        }

        // Set calculated values
        $data['leave_days'] = $leaveDays;
        $data['apply_date'] = $data['apply_date'] ?? date('Y-m-d');
        $data['status'] = 'pending';
        $data['is_active'] = 'yes';

        if ($this->insert($data)) {
            return [
                'success' => true,
                'message' => 'Leave application submitted successfully',
                'data' => $this->find($this->getInsertID())
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to submit leave application',
            'errors' => $this->errors()
        ];
    }

    /**
     * Approve leave request
     */
    public function approveLeave($id, $approvedBy, $adminRemark = '')
    {
        $data = [
            'status' => 'approved',
            'approved_by' => $approvedBy,
            'approved_date' => date('Y-m-d H:i:s'),
            'admin_remark' => $adminRemark
        ];

        if ($this->update($id, $data)) {
            return [
                'success' => true,
                'message' => 'Leave request approved successfully'
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to approve leave request'
        ];
    }

    /**
     * Disapprove leave request
     */
    public function disapproveLeave($id, $approvedBy, $adminRemark = '')
    {
        $data = [
            'status' => 'disapproved',
            'approved_by' => $approvedBy,
            'approved_date' => date('Y-m-d H:i:s'),
            'admin_remark' => $adminRemark
        ];

        if ($this->update($id, $data)) {
            return [
                'success' => true,
                'message' => 'Leave request disapproved successfully'
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to disapprove leave request'
        ];
    }

    /**
     * Cancel leave request
     */
    public function cancelLeave($id, $reason = '')
    {
        $leaveRequest = $this->find($id);
        
        if (!$leaveRequest) {
            return [
                'success' => false,
                'message' => 'Leave request not found'
            ];
        }

        if ($leaveRequest['status'] !== 'pending') {
            return [
                'success' => false,
                'message' => 'Only pending leave requests can be cancelled'
            ];
        }

        $data = [
            'is_active' => 'no',
            'admin_remark' => $reason
        ];

        if ($this->update($id, $data)) {
            return [
                'success' => true,
                'message' => 'Leave request cancelled successfully'
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to cancel leave request'
        ];
    }

    /**
     * Get leave statistics
     */
    public function getStatistics($filters = [])
    {
        $stats = [];

        // Total leave requests
        $builder = $this->where('is_active', 'yes');
        if (!empty($filters['year'])) {
            $builder->where('YEAR(leave_from)', $filters['year']);
        }
        if (!empty($filters['department_id'])) {
            $builder->join('staff', 'staff_leave_request.staff_id = staff.id')
                   ->where('staff.department', $filters['department_id']);
        }
        $stats['total_requests'] = $builder->countAllResults();

        // Requests by status
        $statusStats = $this->select('status, COUNT(*) as count')
                           ->where('is_active', 'yes')
                           ->groupBy('status');
        
        if (!empty($filters['year'])) {
            $statusStats->where('YEAR(leave_from)', $filters['year']);
        }
        
        $stats['by_status'] = $statusStats->findAll();

        // Total leave days taken
        $leaveDaysStats = $this->selectSum('leave_days', 'total_days')
                              ->where('status', 'approved')
                              ->where('is_active', 'yes');
        
        if (!empty($filters['year'])) {
            $leaveDaysStats->where('YEAR(leave_from)', $filters['year']);
        }
        
        $leaveDaysResult = $leaveDaysStats->first();
        $stats['total_leave_days'] = $leaveDaysResult['total_days'] ?? 0;

        // Most used leave types
        $stats['by_leave_type'] = $this->db->table($this->table)
                                          ->select('staff_leave_types.type, COUNT(staff_leave_request.id) as count,
                                                   SUM(staff_leave_request.leave_days) as total_days')
                                          ->join('staff_leave_types', 'staff_leave_request.leave_type_id = staff_leave_types.id')
                                          ->where('staff_leave_request.is_active', 'yes')
                                          ->groupBy('staff_leave_types.id')
                                          ->orderBy('count', 'DESC')
                                          ->get()
                                          ->getResultArray();

        return $stats;
    }

    /**
     * Get pending leave requests
     */
    public function getPendingRequests($departmentId = null)
    {
        $filters = ['status' => 'pending'];
        if ($departmentId) {
            $filters['department_id'] = $departmentId;
        }

        return $this->getLeaveRequestsWithDetails($filters);
    }

    /**
     * Get staff leave history
     */
    public function getStaffLeaveHistory($staffId, $year = null)
    {
        $filters = ['staff_id' => $staffId];
        if ($year) {
            $filters['year'] = $year;
        }

        return $this->getLeaveRequestsWithDetails($filters);
    }

    /**
     * Get monthly leave report
     */
    public function getMonthlyLeaveReport($month, $year, $departmentId = null)
    {
        $startDate = date('Y-m-01', mktime(0, 0, 0, $month, 1, $year));
        $endDate = date('Y-m-t', mktime(0, 0, 0, $month, 1, $year));

        $filters = [
            'start_date' => $startDate,
            'end_date' => $endDate,
            'status' => 'approved'
        ];

        if ($departmentId) {
            $filters['department_id'] = $departmentId;
        }

        return $this->getLeaveRequestsWithDetails($filters);
    }

    /**
     * Check for overlapping leave requests
     */
    private function checkOverlappingLeave($staffId, $leaveFrom, $leaveTo, $excludeId = null)
    {
        $builder = $this->where('staff_id', $staffId)
                       ->where('is_active', 'yes')
                       ->where('status !=', 'disapproved')
                       ->groupStart()
                           ->where('leave_from <=', $leaveTo)
                           ->where('leave_to >=', $leaveFrom)
                       ->groupEnd();

        if ($excludeId) {
            $builder->where('id !=', $excludeId);
        }

        return $builder->first() !== null;
    }

    /**
     * Get staff on leave for a specific date
     */
    public function getStaffOnLeave($date)
    {
        return $this->getLeaveRequestsWithDetails([
            'start_date' => $date,
            'end_date' => $date,
            'status' => 'approved'
        ]);
    }

    /**
     * Update leave request
     */
    public function updateLeaveRequest($id, $data)
    {
        $leaveRequest = $this->find($id);
        
        if (!$leaveRequest) {
            return [
                'success' => false,
                'message' => 'Leave request not found'
            ];
        }

        if ($leaveRequest['status'] !== 'pending') {
            return [
                'success' => false,
                'message' => 'Only pending leave requests can be updated'
            ];
        }

        // Recalculate leave days if dates are updated
        if (isset($data['leave_from']) && isset($data['leave_to'])) {
            if (strtotime($data['leave_from']) > strtotime($data['leave_to'])) {
                return [
                    'success' => false,
                    'message' => 'Leave end date must be after or equal to start date'
                ];
            }

            $startDate = new \DateTime($data['leave_from']);
            $endDate = new \DateTime($data['leave_to']);
            $interval = $startDate->diff($endDate);
            $data['leave_days'] = $interval->days + 1;

            // Check for overlapping leave requests
            $overlapping = $this->checkOverlappingLeave($leaveRequest['staff_id'], $data['leave_from'], $data['leave_to'], $id);
            if ($overlapping) {
                return [
                    'success' => false,
                    'message' => 'You already have a leave request for overlapping dates'
                ];
            }
        }

        if ($this->update($id, $data)) {
            return [
                'success' => true,
                'message' => 'Leave request updated successfully',
                'data' => $this->find($id)
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to update leave request',
            'errors' => $this->errors()
        ];
    }
}
