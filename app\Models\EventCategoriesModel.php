<?php

namespace App\Models;

use CodeIgniter\Model;

class EventCategoriesModel extends Model
{
    protected $table = 'event_categories';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'name', 'description', 'color', 'icon', 'requires_approval', 'is_active'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    protected $validationRules = [
        'name' => 'required|min_length[3]|max_length[100]|is_unique[event_categories.name,id,{id}]',
        'color' => 'required|regex_match[/^#[0-9A-Fa-f]{6}$/]',
        'requires_approval' => 'permit_empty|in_list[0,1]',
        'is_active' => 'permit_empty|in_list[yes,no]'
    ];

    protected $validationMessages = [
        'name' => [
            'required' => 'Category name is required',
            'min_length' => 'Category name must be at least 3 characters long',
            'max_length' => 'Category name cannot exceed 100 characters',
            'is_unique' => 'Category name already exists'
        ],
        'color' => [
            'required' => 'Color is required',
            'regex_match' => 'Color must be a valid hex color code (e.g., #007bff)'
        ]
    ];

    /**
     * Get active event categories
     */
    public function getActive()
    {
        return $this->where('is_active', 'yes')
                   ->orderBy('name', 'ASC')
                   ->findAll();
    }

    /**
     * Get categories for dropdown
     */
    public function getForDropdown()
    {
        $categories = $this->getActive();
        
        $dropdown = [];
        foreach ($categories as $category) {
            $dropdown[$category['id']] = $category['name'];
        }
        
        return $dropdown;
    }

    /**
     * Get category with usage count
     */
    public function getWithUsageCount()
    {
        $builder = $this->db->table($this->table);
        $builder->select('event_categories.*, COUNT(school_events.id) as usage_count')
                ->join('school_events', 'event_categories.id = school_events.category_id', 'left')
                ->groupBy('event_categories.id')
                ->orderBy('event_categories.name', 'ASC');

        return $builder->get()->getResultArray();
    }

    /**
     * Check if category can be deleted
     */
    public function canDelete($id)
    {
        $usageCount = $this->db->table('school_events')
                              ->where('category_id', $id)
                              ->countAllResults();

        return $usageCount === 0;
    }

    /**
     * Get categories that require approval
     */
    public function getApprovalRequired()
    {
        return $this->where('requires_approval', 1)
                   ->where('is_active', 'yes')
                   ->findAll();
    }

    /**
     * Get category statistics
     */
    public function getStatistics()
    {
        $totalCategories = $this->where('is_active', 'yes')->countAllResults();
        $approvalRequired = $this->where('requires_approval', 1)
                                ->where('is_active', 'yes')
                                ->countAllResults();

        // Get most used category
        $builder = $this->db->table($this->table);
        $mostUsed = $builder->select('event_categories.name, COUNT(school_events.id) as event_count')
                           ->join('school_events', 'event_categories.id = school_events.category_id', 'left')
                           ->where('event_categories.is_active', 'yes')
                           ->groupBy('event_categories.id')
                           ->orderBy('event_count', 'DESC')
                           ->limit(1)
                           ->get()
                           ->getRowArray();

        return [
            'total_categories' => $totalCategories,
            'approval_required' => $approvalRequired,
            'most_used_category' => $mostUsed['name'] ?? 'None',
            'most_used_count' => $mostUsed['event_count'] ?? 0
        ];
    }

    /**
     * Create category with validation
     */
    public function createCategory($data)
    {
        // Set defaults
        $data['is_active'] = $data['is_active'] ?? 'yes';
        $data['requires_approval'] = $data['requires_approval'] ?? 0;

        if ($this->insert($data)) {
            return [
                'success' => true,
                'message' => 'Event category created successfully',
                'data' => $this->find($this->getInsertID())
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to create event category',
            'errors' => $this->errors()
        ];
    }

    /**
     * Update category with validation
     */
    public function updateCategory($id, $data)
    {
        if ($this->update($id, $data)) {
            return [
                'success' => true,
                'message' => 'Event category updated successfully',
                'data' => $this->find($id)
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to update event category',
            'errors' => $this->errors()
        ];
    }

    /**
     * Delete category with validation
     */
    public function deleteCategory($id)
    {
        if (!$this->canDelete($id)) {
            return [
                'success' => false,
                'message' => 'Cannot delete category. It is being used by existing events.'
            ];
        }

        if ($this->delete($id)) {
            return [
                'success' => true,
                'message' => 'Event category deleted successfully'
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to delete event category'
        ];
    }

    /**
     * Get default categories for seeding
     */
    public function getDefaultCategories()
    {
        return [
            [
                'name' => 'Academic Events',
                'description' => 'Academic activities, exams, orientations',
                'color' => '#007bff',
                'icon' => 'fas fa-graduation-cap',
                'requires_approval' => 0
            ],
            [
                'name' => 'Sports Events',
                'description' => 'Sports competitions and physical activities',
                'color' => '#28a745',
                'icon' => 'fas fa-futbol',
                'requires_approval' => 0
            ],
            [
                'name' => 'Cultural Events',
                'description' => 'Cultural programs, festivals, celebrations',
                'color' => '#e83e8c',
                'icon' => 'fas fa-theater-masks',
                'requires_approval' => 0
            ],
            [
                'name' => 'Administrative',
                'description' => 'Staff meetings, administrative activities',
                'color' => '#6c757d',
                'icon' => 'fas fa-users-cog',
                'requires_approval' => 1
            ],
            [
                'name' => 'Parent Meetings',
                'description' => 'Parent-teacher meetings, conferences',
                'color' => '#fd7e14',
                'icon' => 'fas fa-handshake',
                'requires_approval' => 0
            ],
            [
                'name' => 'Extracurricular',
                'description' => 'Clubs, societies, hobby activities',
                'color' => '#20c997',
                'icon' => 'fas fa-puzzle-piece',
                'requires_approval' => 0
            ],
            [
                'name' => 'Announcements',
                'description' => 'Important announcements and notices',
                'color' => '#ffc107',
                'icon' => 'fas fa-bullhorn',
                'requires_approval' => 1
            ]
        ];
    }

    /**
     * Bulk create default categories
     */
    public function createDefaultCategories()
    {
        $defaultCategories = $this->getDefaultCategories();
        $createdCount = 0;

        foreach ($defaultCategories as $category) {
            // Check if category already exists
            $existing = $this->where('name', $category['name'])->first();
            
            if (!$existing) {
                $category['is_active'] = 'yes';
                if ($this->insert($category)) {
                    $createdCount++;
                }
            }
        }

        return [
            'success' => true,
            'created_count' => $createdCount,
            'message' => "Created {$createdCount} default event categories"
        ];
    }
}
