<?php

namespace App\Models;

use CodeIgniter\Model;

class EventRegistrationsModel extends Model
{
    protected $table = 'event_registrations';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'event_id', 'user_id', 'registration_date', 'status', 'notes'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    protected $validationRules = [
        'event_id' => 'required|integer',
        'user_id' => 'required|integer',
        'status' => 'permit_empty|in_list[registered,attended,cancelled,no_show]'
    ];

    protected $validationMessages = [
        'event_id' => [
            'required' => 'Event ID is required',
            'integer' => 'Invalid event ID'
        ],
        'user_id' => [
            'required' => 'User ID is required',
            'integer' => 'Invalid user ID'
        ]
    ];

    /**
     * Register user for event
     */
    public function registerUser($eventId, $userId, $notes = null)
    {
        // Check if user is already registered
        $existing = $this->where('event_id', $eventId)
                        ->where('user_id', $userId)
                        ->first();

        if ($existing) {
            return [
                'success' => false,
                'message' => 'User is already registered for this event'
            ];
        }

        // Check event capacity
        $eventModel = new SchoolEventsModel();
        $event = $eventModel->find($eventId);
        
        if (!$event) {
            return [
                'success' => false,
                'message' => 'Event not found'
            ];
        }

        // Check if registration is still open
        if ($event['registration_deadline'] && date('Y-m-d') > $event['registration_deadline']) {
            return [
                'success' => false,
                'message' => 'Registration deadline has passed'
            ];
        }

        // Check capacity if max_participants is set
        if ($event['max_participants']) {
            $currentRegistrations = $this->where('event_id', $eventId)
                                        ->where('status', 'registered')
                                        ->countAllResults();
            
            if ($currentRegistrations >= $event['max_participants']) {
                return [
                    'success' => false,
                    'message' => 'Event is full. Maximum participants reached.'
                ];
            }
        }

        $data = [
            'event_id' => $eventId,
            'user_id' => $userId,
            'registration_date' => date('Y-m-d H:i:s'),
            'status' => 'registered',
            'notes' => $notes
        ];

        if ($this->insert($data)) {
            return [
                'success' => true,
                'message' => 'Successfully registered for the event',
                'data' => $this->find($this->getInsertID())
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to register for the event',
            'errors' => $this->errors()
        ];
    }

    /**
     * Cancel registration
     */
    public function cancelRegistration($eventId, $userId)
    {
        $registration = $this->where('event_id', $eventId)
                            ->where('user_id', $userId)
                            ->first();

        if (!$registration) {
            return [
                'success' => false,
                'message' => 'Registration not found'
            ];
        }

        if ($registration['status'] === 'cancelled') {
            return [
                'success' => false,
                'message' => 'Registration is already cancelled'
            ];
        }

        $data = [
            'status' => 'cancelled'
        ];

        if ($this->update($registration['id'], $data)) {
            return [
                'success' => true,
                'message' => 'Registration cancelled successfully'
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to cancel registration'
        ];
    }

    /**
     * Mark attendance
     */
    public function markAttendance($eventId, $userId, $attended = true)
    {
        $registration = $this->where('event_id', $eventId)
                            ->where('user_id', $userId)
                            ->first();

        if (!$registration) {
            return [
                'success' => false,
                'message' => 'Registration not found'
            ];
        }

        $status = $attended ? 'attended' : 'no_show';

        if ($this->update($registration['id'], ['status' => $status])) {
            return [
                'success' => true,
                'message' => 'Attendance marked successfully'
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to mark attendance'
        ];
    }

    /**
     * Get event registrations with user details
     */
    public function getEventRegistrations($eventId, $status = null)
    {
        $builder = $this->db->table($this->table);
        $builder->select('event_registrations.*, users.firstname, users.lastname, users.email')
                ->join('users', 'event_registrations.user_id = users.id')
                ->where('event_registrations.event_id', $eventId);

        if ($status) {
            $builder->where('event_registrations.status', $status);
        }

        $builder->orderBy('event_registrations.registration_date', 'ASC');

        return $builder->get()->getResultArray();
    }

    /**
     * Get user registrations with event details
     */
    public function getUserRegistrations($userId, $status = null)
    {
        $builder = $this->db->table($this->table);
        $builder->select('event_registrations.*, school_events.title, school_events.start_date, 
                         school_events.end_date, school_events.start_time, school_events.end_time,
                         school_events.location, event_categories.name as category_name,
                         event_categories.color')
                ->join('school_events', 'event_registrations.event_id = school_events.id')
                ->join('event_categories', 'school_events.category_id = event_categories.id')
                ->where('event_registrations.user_id', $userId);

        if ($status) {
            $builder->where('event_registrations.status', $status);
        }

        $builder->orderBy('school_events.start_date', 'DESC');

        return $builder->get()->getResultArray();
    }

    /**
     * Get registration statistics for an event
     */
    public function getEventRegistrationStats($eventId)
    {
        $totalRegistrations = $this->where('event_id', $eventId)->countAllResults();
        
        $registeredCount = $this->where('event_id', $eventId)
                               ->where('status', 'registered')
                               ->countAllResults();
        
        $attendedCount = $this->where('event_id', $eventId)
                             ->where('status', 'attended')
                             ->countAllResults();
        
        $cancelledCount = $this->where('event_id', $eventId)
                              ->where('status', 'cancelled')
                              ->countAllResults();
        
        $noShowCount = $this->where('event_id', $eventId)
                           ->where('status', 'no_show')
                           ->countAllResults();

        return [
            'total_registrations' => $totalRegistrations,
            'registered' => $registeredCount,
            'attended' => $attendedCount,
            'cancelled' => $cancelledCount,
            'no_show' => $noShowCount,
            'attendance_rate' => $totalRegistrations > 0 ? round(($attendedCount / $totalRegistrations) * 100, 2) : 0
        ];
    }

    /**
     * Get user registration statistics
     */
    public function getUserRegistrationStats($userId)
    {
        $totalRegistrations = $this->where('user_id', $userId)->countAllResults();
        
        $attendedCount = $this->where('user_id', $userId)
                             ->where('status', 'attended')
                             ->countAllResults();
        
        $cancelledCount = $this->where('user_id', $userId)
                              ->where('status', 'cancelled')
                              ->countAllResults();
        
        $noShowCount = $this->where('user_id', $userId)
                           ->where('status', 'no_show')
                           ->countAllResults();

        return [
            'total_registrations' => $totalRegistrations,
            'attended' => $attendedCount,
            'cancelled' => $cancelledCount,
            'no_show' => $noShowCount,
            'attendance_rate' => $totalRegistrations > 0 ? round(($attendedCount / $totalRegistrations) * 100, 2) : 0
        ];
    }

    /**
     * Check if user is registered for event
     */
    public function isUserRegistered($eventId, $userId)
    {
        $registration = $this->where('event_id', $eventId)
                            ->where('user_id', $userId)
                            ->where('status !=', 'cancelled')
                            ->first();

        return $registration !== null;
    }

    /**
     * Get registration status for user and event
     */
    public function getRegistrationStatus($eventId, $userId)
    {
        $registration = $this->where('event_id', $eventId)
                            ->where('user_id', $userId)
                            ->first();

        return $registration ? $registration['status'] : null;
    }

    /**
     * Bulk mark attendance
     */
    public function bulkMarkAttendance($eventId, $userIds, $attended = true)
    {
        $status = $attended ? 'attended' : 'no_show';
        $updatedCount = 0;

        foreach ($userIds as $userId) {
            $registration = $this->where('event_id', $eventId)
                                ->where('user_id', $userId)
                                ->first();

            if ($registration && $this->update($registration['id'], ['status' => $status])) {
                $updatedCount++;
            }
        }

        return [
            'success' => true,
            'message' => "Attendance marked for {$updatedCount} participants",
            'updated_count' => $updatedCount
        ];
    }

    /**
     * Get upcoming registrations for user
     */
    public function getUpcomingRegistrations($userId, $limit = 5)
    {
        $today = date('Y-m-d');

        $builder = $this->db->table($this->table);
        $builder->select('event_registrations.*, school_events.title, school_events.start_date, 
                         school_events.end_date, school_events.start_time, school_events.location,
                         event_categories.name as category_name, event_categories.color')
                ->join('school_events', 'event_registrations.event_id = school_events.id')
                ->join('event_categories', 'school_events.category_id = event_categories.id')
                ->where('event_registrations.user_id', $userId)
                ->where('event_registrations.status', 'registered')
                ->where('school_events.start_date >=', $today)
                ->where('school_events.is_active', 'yes')
                ->orderBy('school_events.start_date', 'ASC')
                ->limit($limit);

        return $builder->get()->getResultArray();
    }
}
