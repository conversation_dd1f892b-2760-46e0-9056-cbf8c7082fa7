<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class TestCalendar extends BaseCommand
{
    protected $group       = 'test';
    protected $name        = 'test:calendar';
    protected $description = 'Test academic calendar functionality';

    public function run(array $params)
    {
        CLI::write('=== Academic Calendar Test ===', 'green');
        CLI::newLine();

        // Load helpers
        helper(['calendar', 'session']);

        // Test 1: Check if Independence Day is a holiday
        CLI::write('Test 1: Check Independence Day (2024-08-14)', 'yellow');
        
        try {
            $isHoliday = is_school_holiday('2024-08-14');
            CLI::write('Is Holiday: ' . ($isHoliday ? 'Yes' : 'No'));

            $affectsAttendance = affects_attendance('2024-08-14');
            CLI::write('Affects Attendance: ' . ($affectsAttendance ? 'Yes' : 'No'));

            $validation = validate_attendance_date('2024-08-14');
            CLI::write('Validation Result:');
            CLI::write('  - Is Valid: ' . ($validation['is_valid'] ? 'Yes' : 'No'));
            CLI::write('  - Is Holiday: ' . ($validation['is_holiday'] ? 'Yes' : 'No'));
            CLI::write('  - Message: ' . $validation['message']);
        } catch (\Exception $e) {
            CLI::write('Error in Test 1: ' . $e->getMessage(), 'red');
        }
        
        CLI::newLine();

        // Test 2: Check a regular working day
        CLI::write('Test 2: Check Regular Day (2024-09-10)', 'yellow');
        
        try {
            $isHoliday2 = is_school_holiday('2024-09-10');
            CLI::write('Is Holiday: ' . ($isHoliday2 ? 'Yes' : 'No'));

            $validation2 = validate_attendance_date('2024-09-10');
            CLI::write('Validation Result:');
            CLI::write('  - Is Valid: ' . ($validation2['is_valid'] ? 'Yes' : 'No'));
            CLI::write('  - Is Weekend: ' . ($validation2['is_weekend'] ? 'Yes' : 'No'));
            CLI::write('  - Message: ' . $validation2['message']);
        } catch (\Exception $e) {
            CLI::write('Error in Test 2: ' . $e->getMessage(), 'red');
        }
        
        CLI::newLine();

        // Test 3: Get working days in August 2024
        CLI::write('Test 3: Working Days in August 2024', 'yellow');
        
        try {
            $workingDays = get_working_days('2024-08-01', '2024-08-31');
            CLI::write('Total Working Days: ' . count($workingDays));
            CLI::write('First 5 Working Days: ' . implode(', ', array_slice($workingDays, 0, 5)));
        } catch (\Exception $e) {
            CLI::write('Error in Test 3: ' . $e->getMessage(), 'red');
        }
        
        CLI::newLine();

        // Test 4: Get upcoming holidays
        CLI::write('Test 4: Upcoming Holidays', 'yellow');
        
        try {
            $upcomingHolidays = get_upcoming_holidays(3);
            CLI::write('Found ' . count($upcomingHolidays) . ' upcoming holidays:');
            foreach ($upcomingHolidays as $holiday) {
                CLI::write("  - {$holiday['title']} ({$holiday['start_date']})");
            }
        } catch (\Exception $e) {
            CLI::write('Error in Test 4: ' . $e->getMessage(), 'red');
        }
        
        CLI::newLine();

        // Test 5: Calendar Service
        CLI::write('Test 5: Calendar Service', 'yellow');
        
        try {
            $calendarService = \App\Services\CalendarService::getInstance();
            $summary = $calendarService->getCalendarSummary();
            CLI::write('Calendar Statistics:');
            CLI::write('  - Total Holidays: ' . $summary['statistics']['total_holidays']);
            CLI::write('  - Upcoming Holidays: ' . $summary['statistics']['upcoming_holidays']);
            CLI::write('  - Academic Breaks: ' . $summary['statistics']['academic_breaks']);
            CLI::write('  - Attendance Affecting: ' . $summary['statistics']['attendance_affecting']);
        } catch (\Exception $e) {
            CLI::write('Error in Test 5: ' . $e->getMessage(), 'red');
        }
        
        CLI::newLine();

        // Test 6: Holiday info
        CLI::write('Test 6: Holiday Information', 'yellow');
        
        try {
            $holidayInfo = get_holiday_info('2024-08-14');
            if ($holidayInfo) {
                CLI::write("Holiday: {$holidayInfo['title']}");
                CLI::write("Type: {$holidayInfo['holiday_type_name']}");
                CLI::write("Description: {$holidayInfo['description']}");
                CLI::write("Affects Attendance: " . ($holidayInfo['affects_attendance'] ? 'Yes' : 'No'));
            } else {
                CLI::write('No holiday found for this date');
            }
        } catch (\Exception $e) {
            CLI::write('Error in Test 6: ' . $e->getMessage(), 'red');
        }
        
        CLI::newLine();

        // Test 7: Models functionality
        CLI::write('Test 7: Model Functionality', 'yellow');
        
        try {
            $calendarModel = new \App\Models\AcademicCalendarModel();
            $holidayTypesModel = new \App\Models\HolidayTypesModel();
            
            $totalHolidays = $calendarModel->countAllResults();
            $totalTypes = $holidayTypesModel->countAllResults();
            
            CLI::write("Total Holidays in Database: {$totalHolidays}");
            CLI::write("Total Holiday Types: {$totalTypes}");
            
            // Test holiday validation
            $testValidation = $calendarModel->validateAttendanceDate('2024-08-14');
            CLI::write('Model Validation Test:');
            CLI::write('  - Is Valid: ' . ($testValidation['is_valid'] ? 'Yes' : 'No'));
            CLI::write('  - Message: ' . $testValidation['message']);
            
        } catch (\Exception $e) {
            CLI::write('Error in Test 7: ' . $e->getMessage(), 'red');
        }
        
        CLI::newLine();

        // Test 8: Attendance integration
        CLI::write('Test 8: Attendance Integration', 'yellow');
        
        try {
            $attendanceModel = new \App\Models\StudentAttendanceModel();
            
            // Test attendance date validation
            $attendanceValidation = $attendanceModel->validateAttendanceDate('2024-08-14');
            CLI::write('Attendance Model Validation:');
            CLI::write('  - Is Valid: ' . ($attendanceValidation['is_valid'] ? 'Yes' : 'No'));
            CLI::write('  - Message: ' . $attendanceValidation['message']);
            
        } catch (\Exception $e) {
            CLI::write('Error in Test 8: ' . $e->getMessage(), 'red');
        }

        CLI::newLine();
        CLI::write('=== Test Complete ===', 'green');
    }
}
