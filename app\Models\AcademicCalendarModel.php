<?php

namespace App\Models;

use CodeIgniter\Model;

class AcademicCalendarModel extends Model
{
    protected $table = 'academic_calendar';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'session_id', 'holiday_type_id', 'title', 'description', 'start_date', 'end_date',
        'is_recurring', 'recurrence_pattern', 'applies_to', 'class_ids', 
        'notification_sent', 'created_by', 'is_active'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    protected $validationRules = [
        'session_id' => 'required|integer',
        'holiday_type_id' => 'required|integer',
        'title' => 'required|min_length[3]|max_length[200]',
        'start_date' => 'required|valid_date',
        'end_date' => 'required|valid_date',
        'applies_to' => 'permit_empty|in_list[all,students,staff,specific_classes]',
        'is_recurring' => 'permit_empty|in_list[0,1]',
        'is_active' => 'permit_empty|in_list[yes,no]'
    ];

    protected $validationMessages = [
        'session_id' => [
            'required' => 'Academic session is required',
            'integer' => 'Invalid session selected'
        ],
        'holiday_type_id' => [
            'required' => 'Holiday type is required',
            'integer' => 'Invalid holiday type selected'
        ],
        'title' => [
            'required' => 'Holiday title is required',
            'min_length' => 'Title must be at least 3 characters long',
            'max_length' => 'Title cannot exceed 200 characters'
        ],
        'start_date' => [
            'required' => 'Start date is required',
            'valid_date' => 'Please enter a valid start date'
        ],
        'end_date' => [
            'required' => 'End date is required',
            'valid_date' => 'Please enter a valid end date'
        ]
    ];

    /**
     * Get calendar events with details
     */
    public function getCalendarEvents($filters = [])
    {
        helper('session');
        
        $builder = $this->db->table($this->table);
        $builder->select('academic_calendar.*, holiday_types.name as holiday_type_name, 
                         holiday_types.color, holiday_types.is_academic_break, 
                         holiday_types.affects_attendance, sessions.session as session_name')
                ->join('holiday_types', 'academic_calendar.holiday_type_id = holiday_types.id')
                ->join('sessions', 'academic_calendar.session_id = sessions.id')
                ->where('academic_calendar.is_active', 'yes');

        // Apply session filter
        $sessionId = $filters['session_id'] ?? session()->get('current_session_id') ?? get_current_session_id();
        if ($sessionId) {
            $builder->where('academic_calendar.session_id', $sessionId);
        }

        // Apply date filters
        if (!empty($filters['start_date'])) {
            $builder->where('academic_calendar.end_date >=', $filters['start_date']);
        }
        
        if (!empty($filters['end_date'])) {
            $builder->where('academic_calendar.start_date <=', $filters['end_date']);
        }
        
        if (!empty($filters['holiday_type_id'])) {
            $builder->where('academic_calendar.holiday_type_id', $filters['holiday_type_id']);
        }

        if (!empty($filters['applies_to'])) {
            $builder->where('academic_calendar.applies_to', $filters['applies_to']);
        }

        return $builder->orderBy('academic_calendar.start_date', 'ASC')->get()->getResultArray();
    }

    /**
     * Check if a date is a holiday
     */
    public function isHoliday($date, $sessionId = null, $appliesTo = 'all')
    {
        helper('session');
        
        if (!$sessionId) {
            $sessionId = get_current_session_id();
        }

        $builder = $this->where('session_id', $sessionId)
                       ->where('is_active', 'yes')
                       ->where('start_date <=', $date)
                       ->where('end_date >=', $date);

        // Filter by applies_to
        if ($appliesTo !== 'all') {
            $builder->groupStart()
                   ->where('applies_to', 'all')
                   ->orWhere('applies_to', $appliesTo)
                   ->groupEnd();
        }

        return $builder->countAllResults() > 0;
    }

    /**
     * Get holidays for a date range
     */
    public function getHolidaysInRange($startDate, $endDate, $sessionId = null)
    {
        helper('session');
        
        if (!$sessionId) {
            $sessionId = get_current_session_id();
        }

        return $this->getCalendarEvents([
            'session_id' => $sessionId,
            'start_date' => $startDate,
            'end_date' => $endDate
        ]);
    }

    /**
     * Get holidays that affect attendance
     */
    public function getAttendanceAffectingHolidays($startDate, $endDate, $sessionId = null)
    {
        helper('session');
        
        if (!$sessionId) {
            $sessionId = get_current_session_id();
        }

        $builder = $this->db->table($this->table);
        $builder->select('academic_calendar.*, holiday_types.affects_attendance')
                ->join('holiday_types', 'academic_calendar.holiday_type_id = holiday_types.id')
                ->where('academic_calendar.session_id', $sessionId)
                ->where('academic_calendar.is_active', 'yes')
                ->where('holiday_types.affects_attendance', 1)
                ->where('academic_calendar.start_date <=', $endDate)
                ->where('academic_calendar.end_date >=', $startDate);

        return $builder->get()->getResultArray();
    }

    /**
     * Get upcoming holidays
     */
    public function getUpcomingHolidays($limit = 5, $sessionId = null)
    {
        helper('session');

        if (!$sessionId) {
            $sessionId = get_current_session_id();
        }

        $today = date('Y-m-d');

        $events = $this->getCalendarEvents([
            'session_id' => $sessionId,
            'start_date' => $today
        ]);

        // Apply limit
        return array_slice($events, 0, $limit);
    }

    /**
     * Create holiday with validation
     */
    public function createHoliday($data)
    {
        // Validate date range
        if (strtotime($data['start_date']) > strtotime($data['end_date'])) {
            return [
                'success' => false,
                'message' => 'End date must be after or equal to start date'
            ];
        }

        // Check for overlapping holidays of same type
        $overlapping = $this->checkOverlappingHolidays(
            $data['session_id'],
            $data['holiday_type_id'],
            $data['start_date'],
            $data['end_date']
        );

        if ($overlapping) {
            return [
                'success' => false,
                'message' => 'Holiday overlaps with existing holiday of same type'
            ];
        }

        // Set defaults
        $data['is_active'] = $data['is_active'] ?? 'yes';
        $data['applies_to'] = $data['applies_to'] ?? 'all';
        $data['notification_sent'] = 0;

        $result = $this->insert($data);

        if ($result) {
            return [
                'success' => true,
                'message' => 'Holiday created successfully',
                'data' => $this->find($this->getInsertID())
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to create holiday'
        ];
    }

    /**
     * Check for overlapping holidays
     */
    public function checkOverlappingHolidays($sessionId, $holidayTypeId, $startDate, $endDate, $excludeId = null)
    {
        $builder = $this->where('session_id', $sessionId)
                       ->where('holiday_type_id', $holidayTypeId)
                       ->where('is_active', 'yes')
                       ->groupStart()
                           ->where('start_date <=', $endDate)
                           ->where('end_date >=', $startDate)
                       ->groupEnd();

        if ($excludeId) {
            $builder->where('id !=', $excludeId);
        }

        return $builder->countAllResults() > 0;
    }

    /**
     * Get calendar statistics
     */
    public function getStatistics($sessionId = null)
    {
        helper('session');
        
        if (!$sessionId) {
            $sessionId = get_current_session_id();
        }

        $stats = [
            'total_holidays' => $this->where('session_id', $sessionId)->where('is_active', 'yes')->countAllResults(),
            'upcoming_holidays' => $this->where('session_id', $sessionId)
                                       ->where('is_active', 'yes')
                                       ->where('start_date >=', date('Y-m-d'))
                                       ->countAllResults(),
            'academic_breaks' => $this->db->table($this->table)
                                         ->join('holiday_types', 'academic_calendar.holiday_type_id = holiday_types.id')
                                         ->where('academic_calendar.session_id', $sessionId)
                                         ->where('academic_calendar.is_active', 'yes')
                                         ->where('holiday_types.is_academic_break', 1)
                                         ->countAllResults(),
            'attendance_affecting' => $this->db->table($this->table)
                                            ->join('holiday_types', 'academic_calendar.holiday_type_id = holiday_types.id')
                                            ->where('academic_calendar.session_id', $sessionId)
                                            ->where('academic_calendar.is_active', 'yes')
                                            ->where('holiday_types.affects_attendance', 1)
                                            ->countAllResults()
        ];

        return $stats;
    }

    /**
     * Generate recurring holidays for next year
     */
    public function generateRecurringHolidays($fromSessionId, $toSessionId)
    {
        $recurringHolidays = $this->where('session_id', $fromSessionId)
                                 ->where('is_recurring', 1)
                                 ->where('is_active', 'yes')
                                 ->findAll();

        $createdCount = 0;
        $errors = [];

        foreach ($recurringHolidays as $holiday) {
            try {
                // Calculate new dates (add 1 year)
                $newStartDate = date('Y-m-d', strtotime($holiday['start_date'] . ' +1 year'));
                $newEndDate = date('Y-m-d', strtotime($holiday['end_date'] . ' +1 year'));

                $newHoliday = [
                    'session_id' => $toSessionId,
                    'holiday_type_id' => $holiday['holiday_type_id'],
                    'title' => $holiday['title'],
                    'description' => $holiday['description'],
                    'start_date' => $newStartDate,
                    'end_date' => $newEndDate,
                    'is_recurring' => $holiday['is_recurring'],
                    'recurrence_pattern' => $holiday['recurrence_pattern'],
                    'applies_to' => $holiday['applies_to'],
                    'class_ids' => $holiday['class_ids'],
                    'created_by' => $holiday['created_by'],
                    'is_active' => 'yes'
                ];

                if ($this->insert($newHoliday)) {
                    $createdCount++;
                }
            } catch (\Exception $e) {
                $errors[] = "Error creating recurring holiday '{$holiday['title']}': " . $e->getMessage();
            }
        }

        return [
            'success' => true,
            'created_count' => $createdCount,
            'errors' => $errors,
            'message' => "Generated {$createdCount} recurring holidays for new session"
        ];
    }

    /**
     * Validate attendance date
     */
    public function validateAttendanceDate($date, $sessionId = null)
    {
        helper('calendar');
        return validate_attendance_date($date, $sessionId);
    }
}
