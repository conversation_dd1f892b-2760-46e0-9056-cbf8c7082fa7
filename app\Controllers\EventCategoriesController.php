<?php

namespace App\Controllers;

use App\Models\EventCategoriesModel;

class EventCategoriesController extends BaseCrudController
{
    public function __construct()
    {
        parent::__construct();
        $this->model = new EventCategoriesModel();
        
        $this->viewPath = 'admin/event_categories';
        $this->routePrefix = 'admin/event-categories';
        $this->entityName = 'Event Category';
        $this->entityNamePlural = 'Event Categories';
    }

    /**
     * Display categories list
     */
    public function index()
    {
        $data = [
            'title' => 'Event Categories',
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => 'Event Categories', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/index', $data);
    }

    /**
     * Get categories data for DataTables
     */
    public function getData()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $categories = $this->model->getWithUsageCount();

        $data = [];
        foreach ($categories as $category) {
            $data[] = [
                'id' => $category['id'],
                'name' => $category['name'],
                'description' => $category['description'] ?? 'No description',
                'color' => '<span class="badge" style="background-color: ' . $category['color'] . '; color: white;">' . $category['color'] . '</span>',
                'icon' => $category['icon'] ? '<i class="' . $category['icon'] . '"></i>' : 'No icon',
                'requires_approval' => $category['requires_approval'] ? 'Yes' : 'No',
                'usage_count' => $category['usage_count'],
                'is_active' => $category['is_active'] === 'yes' ? 
                    '<span class="badge badge-success">Active</span>' : 
                    '<span class="badge badge-secondary">Inactive</span>',
                'actions' => $this->generateActionButtons($category)
            ];
        }

        return $this->response->setJSON(['data' => $data]);
    }

    /**
     * Show create form
     */
    public function create()
    {
        $data = [
            'title' => 'Create ' . $this->entityName,
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => base_url($this->routePrefix)],
                ['name' => 'Create', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/create', $data);
    }

    /**
     * Store new category
     */
    public function store()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $data = $this->request->getPost();
        $result = $this->model->createCategory($data);
        
        return $this->response->setJSON($result);
    }

    /**
     * Show edit form
     */
    public function edit($id)
    {
        $category = $this->model->find($id);
        
        if (!$category) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Category not found');
        }

        $data = [
            'title' => 'Edit ' . $this->entityName,
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'category' => $category,
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => base_url($this->routePrefix)],
                ['name' => 'Edit', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/edit', $data);
    }

    /**
     * Update category
     */
    public function update($id)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $data = $this->request->getPost();
        $result = $this->model->updateCategory($id, $data);
        
        return $this->response->setJSON($result);
    }

    /**
     * Delete category
     */
    public function delete($id)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $result = $this->model->deleteCategory($id);
        return $this->response->setJSON($result);
    }

    /**
     * Get categories for dropdown
     */
    public function getForDropdown()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $categories = $this->model->getForDropdown();
        return $this->response->setJSON($categories);
    }

    /**
     * Create default categories
     */
    public function createDefaults()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $result = $this->model->createDefaultCategories();
        return $this->response->setJSON($result);
    }

    /**
     * Get category statistics
     */
    public function getStatistics()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $stats = $this->model->getStatistics();
        return $this->response->setJSON($stats);
    }

    /**
     * Generate action buttons for DataTable
     */
    private function generateActionButtons($category)
    {
        $buttons = [];
        
        // Edit button
        $buttons[] = '<a href="' . base_url($this->routePrefix . '/' . $category['id'] . '/edit') . '" 
                        class="btn btn-sm btn-warning" title="Edit">
                        <i class="fas fa-edit"></i>
                      </a>';
        
        // Delete button (only if not in use)
        if ($category['usage_count'] == 0) {
            $buttons[] = '<button onclick="deleteCategory(' . $category['id'] . ')" 
                            class="btn btn-sm btn-danger" title="Delete">
                            <i class="fas fa-trash"></i>
                          </button>';
        } else {
            $buttons[] = '<button class="btn btn-sm btn-secondary" disabled title="Cannot delete - in use">
                            <i class="fas fa-trash"></i>
                          </button>';
        }
        
        return implode(' ', $buttons);
    }
}
