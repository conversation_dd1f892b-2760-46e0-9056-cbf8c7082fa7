<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class EventCalendarSeeder extends Seeder
{
    public function run()
    {
        // Seed event categories
        $this->seedEventCategories();
        
        // Seed sample school events
        $this->seedSampleEvents();
        
        echo "Event Calendar system seeded successfully!\n";
    }

    private function seedEventCategories()
    {
        $categories = [
            [
                'name' => 'Academic Events',
                'description' => 'Academic activities, exams, orientations',
                'color' => '#007bff',
                'icon' => 'fas fa-graduation-cap',
                'requires_approval' => 0,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Sports Events',
                'description' => 'Sports competitions and physical activities',
                'color' => '#28a745',
                'icon' => 'fas fa-futbol',
                'requires_approval' => 0,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Cultural Events',
                'description' => 'Cultural programs, festivals, celebrations',
                'color' => '#e83e8c',
                'icon' => 'fas fa-theater-masks',
                'requires_approval' => 0,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Administrative',
                'description' => 'Staff meetings, administrative activities',
                'color' => '#6c757d',
                'icon' => 'fas fa-users-cog',
                'requires_approval' => 1,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Parent Meetings',
                'description' => 'Parent-teacher meetings, conferences',
                'color' => '#fd7e14',
                'icon' => 'fas fa-handshake',
                'requires_approval' => 0,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Extracurricular',
                'description' => 'Clubs, societies, hobby activities',
                'color' => '#20c997',
                'icon' => 'fas fa-puzzle-piece',
                'requires_approval' => 0,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Announcements',
                'description' => 'Important announcements and notices',
                'color' => '#ffc107',
                'icon' => 'fas fa-bullhorn',
                'requires_approval' => 1,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        foreach ($categories as $category) {
            // Check if category already exists
            $existing = $this->db->table('event_categories')
                                ->where('name', $category['name'])
                                ->get()
                                ->getRowArray();
            
            if (!$existing) {
                $this->db->table('event_categories')->insert($category);
                echo "Created event category: {$category['name']}\n";
            }
        }
    }

    private function seedSampleEvents()
    {
        // Get current session
        $session = $this->db->table('sessions')
                           ->where('is_current', 'yes')
                           ->get()
                           ->getRowArray();
        
        if (!$session) {
            $session = $this->db->table('sessions')
                               ->orderBy('id', 'DESC')
                               ->limit(1)
                               ->get()
                               ->getRowArray();
        }

        if (!$session) {
            echo "No session found. Skipping sample events.\n";
            return;
        }

        // Get event categories
        $categories = $this->db->table('event_categories')->get()->getResultArray();
        $categoryMap = [];
        foreach ($categories as $category) {
            $categoryMap[$category['name']] = $category['id'];
        }

        // Get admin user
        $adminUser = $this->db->table('users')
                             ->where('email', '<EMAIL>')
                             ->orWhere('id', 1)
                             ->get()
                             ->getRowArray();

        $createdBy = $adminUser ? $adminUser['id'] : 1;

        $sampleEvents = [
            [
                'session_id' => $session['id'],
                'category_id' => $categoryMap['Academic Events'] ?? 1,
                'title' => 'New Student Orientation',
                'description' => 'Welcome orientation for new students and parents',
                'event_type' => 'academic',
                'start_date' => date('Y-m-d', strtotime('+7 days')),
                'end_date' => date('Y-m-d', strtotime('+7 days')),
                'start_time' => '09:00:00',
                'end_time' => '12:00:00',
                'location' => 'Main Auditorium',
                'organizer_id' => $createdBy,
                'target_audience' => 'students',
                'registration_required' => 1,
                'registration_deadline' => date('Y-m-d', strtotime('+5 days')),
                'approval_status' => 'approved',
                'priority' => 'high',
                'is_featured' => 1,
                'is_active' => 'yes',
                'created_by' => $createdBy,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'session_id' => $session['id'],
                'category_id' => $categoryMap['Sports Events'] ?? 2,
                'title' => 'Annual Sports Day',
                'description' => 'Inter-house sports competition for all students',
                'event_type' => 'sports',
                'start_date' => date('Y-m-d', strtotime('+14 days')),
                'end_date' => date('Y-m-d', strtotime('+14 days')),
                'start_time' => '08:00:00',
                'end_time' => '16:00:00',
                'location' => 'School Sports Ground',
                'organizer_id' => $createdBy,
                'target_audience' => 'all',
                'registration_required' => 0,
                'approval_status' => 'approved',
                'priority' => 'high',
                'is_featured' => 1,
                'is_active' => 'yes',
                'created_by' => $createdBy,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'session_id' => $session['id'],
                'category_id' => $categoryMap['Cultural Events'] ?? 3,
                'title' => 'Cultural Festival',
                'description' => 'Annual cultural festival showcasing student talents',
                'event_type' => 'cultural',
                'start_date' => date('Y-m-d', strtotime('+21 days')),
                'end_date' => date('Y-m-d', strtotime('+23 days')),
                'start_time' => '18:00:00',
                'end_time' => '21:00:00',
                'location' => 'School Amphitheater',
                'organizer_id' => $createdBy,
                'target_audience' => 'all',
                'registration_required' => 1,
                'registration_deadline' => date('Y-m-d', strtotime('+18 days')),
                'approval_status' => 'approved',
                'priority' => 'medium',
                'is_featured' => 1,
                'is_active' => 'yes',
                'created_by' => $createdBy,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'session_id' => $session['id'],
                'category_id' => $categoryMap['Parent Meetings'] ?? 5,
                'title' => 'Parent-Teacher Conference',
                'description' => 'Quarterly parent-teacher meeting to discuss student progress',
                'event_type' => 'meeting',
                'start_date' => date('Y-m-d', strtotime('+30 days')),
                'end_date' => date('Y-m-d', strtotime('+30 days')),
                'start_time' => '14:00:00',
                'end_time' => '17:00:00',
                'location' => 'Individual Classrooms',
                'organizer_id' => $createdBy,
                'target_audience' => 'parents',
                'registration_required' => 1,
                'registration_deadline' => date('Y-m-d', strtotime('+27 days')),
                'approval_status' => 'approved',
                'priority' => 'high',
                'is_featured' => 0,
                'is_active' => 'yes',
                'created_by' => $createdBy,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'session_id' => $session['id'],
                'category_id' => $categoryMap['Extracurricular'] ?? 6,
                'title' => 'Science Club Workshop',
                'description' => 'Hands-on science experiments and demonstrations',
                'event_type' => 'extracurricular',
                'start_date' => date('Y-m-d', strtotime('+10 days')),
                'end_date' => date('Y-m-d', strtotime('+10 days')),
                'start_time' => '15:30:00',
                'end_time' => '17:00:00',
                'location' => 'Science Laboratory',
                'organizer_id' => $createdBy,
                'target_audience' => 'students',
                'max_participants' => 30,
                'registration_required' => 1,
                'registration_deadline' => date('Y-m-d', strtotime('+8 days')),
                'approval_status' => 'approved',
                'priority' => 'medium',
                'is_featured' => 0,
                'is_active' => 'yes',
                'created_by' => $createdBy,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        foreach ($sampleEvents as $event) {
            // Check if event already exists
            $existing = $this->db->table('school_events')
                                ->where('title', $event['title'])
                                ->where('session_id', $event['session_id'])
                                ->get()
                                ->getRowArray();
            
            if (!$existing) {
                $this->db->table('school_events')->insert($event);
                echo "Created sample event: {$event['title']}\n";
            }
        }
    }
}
