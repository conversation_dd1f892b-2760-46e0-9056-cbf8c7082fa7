<?php

namespace App\Models;

class StaffModel extends BaseModel
{
    protected $table = 'staff';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'employee_id', 'lang_id', 'currency_id', 'department', 'designation', 'qualification',
        'work_exp', 'name', 'surname', 'father_name', 'mother_name', 'contact_no',
        'emergency_contact_no', 'email', 'dob', 'marital_status', 'date_of_joining',
        'date_of_leaving', 'local_address', 'permanent_address', 'note', 'image',
        'password', 'gender', 'account_title', 'bank_account_no', 'bank_name',
        'ifsc_code', 'bank_branch', 'payscale', 'basic_salary', 'epf_no',
        'contract_type', 'shift', 'location', 'facebook', 'twitter', 'linkedin',
        'instagram', 'resume', 'joining_letter', 'resignation_letter',
        'other_document_name', 'other_document_file', 'user_id', 'is_active',
        'verification_code', 'disable_at'
    ];

    protected $validationRules = [
        'employee_id' => 'required|is_unique[staff.employee_id,id,{id}]',
        'name' => 'required|min_length[2]|max_length[200]',
        'surname' => 'required|min_length[2]|max_length[200]',
        'email' => 'required|valid_email|is_unique[staff.email,id,{id}]',
        'contact_no' => 'required|numeric|min_length[10]|max_length[15]',
        'dob' => 'required|valid_date',
        'gender' => 'required|in_list[Male,Female,Other]',
        'date_of_joining' => 'permit_empty|valid_date',
        'department' => 'permit_empty|integer',
        'designation' => 'permit_empty|integer',
        'marital_status' => 'permit_empty|in_list[Single,Married,Divorced,Widowed]'
    ];

    protected $validationMessages = [
        'employee_id' => [
            'required' => 'Employee ID is required',
            'is_unique' => 'Employee ID already exists'
        ],
        'name' => [
            'required' => 'Name is required',
            'min_length' => 'Name must be at least 2 characters long',
            'max_length' => 'Name cannot exceed 200 characters'
        ],
        'surname' => [
            'required' => 'Surname is required',
            'min_length' => 'Surname must be at least 2 characters long',
            'max_length' => 'Surname cannot exceed 200 characters'
        ],
        'email' => [
            'required' => 'Email is required',
            'valid_email' => 'Please enter a valid email address',
            'is_unique' => 'Email address already exists'
        ],
        'contact_no' => [
            'required' => 'Contact number is required',
            'numeric' => 'Contact number must contain only numbers',
            'min_length' => 'Contact number must be at least 10 digits',
            'max_length' => 'Contact number cannot exceed 15 digits'
        ]
    ];

    protected $searchableColumns = [
        'employee_id', 'name', 'surname', 'email', 'contact_no'
    ];

    protected $orderableColumns = [
        'id', 'employee_id', 'name', 'surname', 'email', 'contact_no', 'date_of_joining', 'created_at'
    ];

    /**
     * Get staff with department and designation details
     */
    public function getStaffWithDetails()
    {
        return $this->db->table($this->table)
                       ->select('staff.*, department.department_name, staff_designation.designation')
                       ->join('department', 'staff.department = department.id', 'left')
                       ->join('staff_designation', 'staff.designation = staff_designation.id', 'left')
                       ->orderBy('staff.name', 'ASC')
                       ->get()
                       ->getResultArray();
    }

    /**
     * Get staff by employee ID
     */
    public function getByEmployeeId($employeeId)
    {
        return $this->where('employee_id', $employeeId)->first();
    }

    /**
     * Get staff by department
     */
    public function getByDepartment($departmentId)
    {
        return $this->where('department', $departmentId)
                   ->where('is_active', 1)
                   ->orderBy('name', 'ASC')
                   ->findAll();
    }

    /**
     * Get staff by designation
     */
    public function getByDesignation($designationId)
    {
        return $this->where('designation', $designationId)
                   ->where('is_active', 1)
                   ->orderBy('name', 'ASC')
                   ->findAll();
    }

    /**
     * Generate next employee ID
     */
    public function generateEmployeeId($prefix = 'EMP')
    {
        $year = date('Y');
        $lastStaff = $this->select('employee_id')
                         ->like('employee_id', $prefix . $year, 'after')
                         ->orderBy('id', 'DESC')
                         ->first();

        if ($lastStaff && $lastStaff['employee_id']) {
            $lastNumber = (int) substr($lastStaff['employee_id'], -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $year . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get staff statistics
     */
    public function getStatistics()
    {
        $stats = [];
        
        // Total staff
        $stats['total'] = $this->countAllResults();
        
        // Active staff
        $stats['active'] = $this->where('is_active', 1)->countAllResults();
        
        // Staff by gender
        $stats['by_gender'] = $this->select('gender, COUNT(*) as count')
                                  ->groupBy('gender')
                                  ->findAll();
        
        // Staff by department
        $stats['by_department'] = $this->db->table($this->table)
                                          ->select('department.department_name, COUNT(staff.id) as count')
                                          ->join('department', 'staff.department = department.id', 'left')
                                          ->groupBy('department.id')
                                          ->get()
                                          ->getResultArray();
        
        // Staff by designation
        $stats['by_designation'] = $this->db->table($this->table)
                                           ->select('staff_designation.designation, COUNT(staff.id) as count')
                                           ->join('staff_designation', 'staff.designation = staff_designation.id', 'left')
                                           ->groupBy('staff_designation.id')
                                           ->get()
                                           ->getResultArray();
        
        // Recent joinings (last 30 days)
        $stats['recent_joinings'] = $this->where('date_of_joining >=', date('Y-m-d', strtotime('-30 days')))
                                        ->countAllResults();
        
        return $stats;
    }

    /**
     * Search staff
     */
    public function searchStaff($searchTerm, $filters = [])
    {
        $builder = $this->builder();
        
        if (!empty($searchTerm)) {
            $builder->groupStart()
                    ->like('name', $searchTerm)
                    ->orLike('surname', $searchTerm)
                    ->orLike('employee_id', $searchTerm)
                    ->orLike('email', $searchTerm)
                    ->orLike('contact_no', $searchTerm)
                    ->groupEnd();
        }
        
        // Apply additional filters
        if (!empty($filters['department'])) {
            $builder->where('department', $filters['department']);
        }
        
        if (!empty($filters['designation'])) {
            $builder->where('designation', $filters['designation']);
        }
        
        if (!empty($filters['gender'])) {
            $builder->where('gender', $filters['gender']);
        }
        
        if (!empty($filters['is_active'])) {
            $builder->where('is_active', $filters['is_active']);
        }
        
        return $builder->orderBy('name', 'ASC')->get()->getResultArray();
    }

    /**
     * Get staff for dropdown
     */
    public function getForDropdown($departmentId = null)
    {
        $builder = $this->select('id, CONCAT(name, " ", surname) as full_name, employee_id')
                        ->where('is_active', 1);
        
        if ($departmentId) {
            $builder->where('department', $departmentId);
        }
        
        $staff = $builder->orderBy('name', 'ASC')->findAll();
        
        $dropdown = [];
        foreach ($staff as $member) {
            $dropdown[$member['id']] = $member['full_name'] . ' (' . $member['employee_id'] . ')';
        }
        
        return $dropdown;
    }

    /**
     * Get teachers for dropdown
     */
    public function getTeachersForDropdown()
    {
        // Assuming designation ID 1-5 are for teachers
        return $this->db->table($this->table)
                       ->select('staff.id, CONCAT(staff.name, " ", staff.surname) as full_name, staff.employee_id')
                       ->join('staff_designation', 'staff.designation = staff_designation.id')
                       ->where('staff.is_active', 1)
                       ->whereIn('staff.designation', [1, 2, 3, 4, 5]) // Adjust based on your designation IDs
                       ->orderBy('staff.name', 'ASC')
                       ->get()
                       ->getResultArray();
    }

    /**
     * Get staff attendance summary
     */
    public function getAttendanceSummary($staffId, $month = null, $year = null)
    {
        $month = $month ?: date('m');
        $year = $year ?: date('Y');

        return $this->db->table('staff_attendance')
                       ->select('staff_attendance_type.type, COUNT(*) as count')
                       ->join('staff_attendance_type', 'staff_attendance.staff_attendance_type_id = staff_attendance_type.id')
                       ->where('staff_id', $staffId)
                       ->where('MONTH(date)', $month)
                       ->where('YEAR(date)', $year)
                       ->where('staff_attendance.is_active', 1)
                       ->groupBy('staff_attendance_type.id')
                       ->get()
                       ->getResultArray();
    }

    /**
     * Get staff leave summary
     */
    public function getLeaveSummary($staffId, $year = null)
    {
        $year = $year ?: date('Y');

        return $this->db->table('staff_leave_request')
                       ->select('staff_leave_types.type, COUNT(*) as count, SUM(leave_days) as total_days')
                       ->join('staff_leave_types', 'staff_leave_request.leave_type_id = staff_leave_types.id')
                       ->where('staff_id', $staffId)
                       ->where('YEAR(leave_from)', $year)
                       ->where('status', 'approved')
                       ->where('staff_leave_request.is_active', 'yes')
                       ->groupBy('staff_leave_types.id')
                       ->get()
                       ->getResultArray();
    }

    /**
     * Get staff payroll information
     */
    public function getPayrollInfo($staffId)
    {
        return $this->db->table($this->table)
                       ->select('staff.*, staff_payroll.pay_scale, staff_payroll.grade, staff_payroll.basic_salary')
                       ->join('staff_payroll', 'staff.payscale = staff_payroll.id', 'left')
                       ->where('staff.id', $staffId)
                       ->get()
                       ->getRowArray();
    }

    /**
     * Get staff with complete details including relations
     */
    public function getStaffCompleteDetails($staffId)
    {
        $staff = $this->db->table($this->table)
                         ->select('staff.*, department.department_name, staff_designation.designation,
                                  staff_payroll.pay_scale, staff_payroll.grade, staff_payroll.basic_salary')
                         ->join('department', 'staff.department = department.id', 'left')
                         ->join('staff_designation', 'staff.designation = staff_designation.id', 'left')
                         ->join('staff_payroll', 'staff.payscale = staff_payroll.id', 'left')
                         ->where('staff.id', $staffId)
                         ->get()
                         ->getRowArray();

        if ($staff) {
            // Add attendance summary for current month
            $staff['attendance_summary'] = $this->getAttendanceSummary($staffId);

            // Add leave summary for current year
            $staff['leave_summary'] = $this->getLeaveSummary($staffId);

            // Add recent payslips
            $staff['recent_payslips'] = $this->db->table('staff_payslip')
                                                ->where('staff_id', $staffId)
                                                ->orderBy('year', 'DESC')
                                                ->orderBy('month', 'DESC')
                                                ->limit(6)
                                                ->get()
                                                ->getResultArray();
        }

        return $staff;
    }

    /**
     * Get searchable columns for DataTables
     */
    protected function getSearchableColumns()
    {
        return $this->searchableColumns;
    }

    /**
     * Get orderable columns for DataTables
     */
    protected function getOrderableColumns()
    {
        return $this->orderableColumns;
    }

    /**
     * Load relationship data
     */
    protected function loadRelation($record, $relation)
    {
        switch ($relation) {
            case 'department':
                $record['department_data'] = $this->db->table('department')
                    ->where('id', $record['department'])
                    ->get()
                    ->getRowArray();
                break;
                
            case 'designation':
                $record['designation_data'] = $this->db->table('staff_designation')
                    ->where('id', $record['designation'])
                    ->get()
                    ->getRowArray();
                break;
                
            case 'attendance':
                $record['attendance_data'] = $this->db->table('staff_attendance')
                    ->select('staff_attendance.*, staff_attendance_type.type')
                    ->join('staff_attendance_type', 'staff_attendance.staff_attendance_type_id = staff_attendance_type.id')
                    ->where('staff_id', $record['id'])
                    ->orderBy('date', 'DESC')
                    ->limit(10)
                    ->get()
                    ->getResultArray();
                break;
        }
        
        return $record;
    }
}
