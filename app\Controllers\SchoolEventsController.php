<?php

namespace App\Controllers;

use App\Models\SchoolEventsModel;
use App\Models\EventCategoriesModel;
use App\Models\EventRegistrationsModel;
use App\Models\SessionsModel;

class SchoolEventsController extends BaseCrudController
{
    protected $eventCategoriesModel;
    protected $eventRegistrationsModel;
    protected $sessionsModel;

    public function __construct()
    {
        parent::__construct();
        $this->model = new SchoolEventsModel();
        $this->eventCategoriesModel = new EventCategoriesModel();
        $this->eventRegistrationsModel = new EventRegistrationsModel();
        $this->sessionsModel = new SessionsModel();
        
        $this->viewPath = 'admin/school_events';
        $this->routePrefix = 'admin/school-events';
        $this->entityName = 'School Event';
        $this->entityNamePlural = 'School Events';
    }

    /**
     * Display events list
     */
    public function index()
    {
        helper('session');
        
        $data = [
            'title' => 'School Events',
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'event_categories' => $this->eventCategoriesModel->getForDropdown(),
            'sessions' => $this->sessionsModel->getForDropdown(),
            'current_session' => get_current_session(),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => 'School Events', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/index', $data);
    }

    /**
     * Get events data for DataTables
     */
    public function getData()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $filters = [
            'session_id' => $this->request->getGet('session_id'),
            'category_id' => $this->request->getGet('category_id'),
            'event_type' => $this->request->getGet('event_type'),
            'approval_status' => $this->request->getGet('approval_status')
        ];

        $events = $this->model->getEventsWithDetails($filters);

        $data = [];
        foreach ($events as $event) {
            $data[] = [
                'id' => $event['id'],
                'title' => $event['title'],
                'category' => $event['category_name'],
                'event_type' => ucfirst($event['event_type']),
                'start_date' => date('M d, Y', strtotime($event['start_date'])),
                'end_date' => date('M d, Y', strtotime($event['end_date'])),
                'location' => $event['location'] ?? 'Not specified',
                'organizer' => trim($event['organizer_firstname'] . ' ' . $event['organizer_lastname']) ?: 'Not assigned',
                'target_audience' => ucfirst(str_replace('_', ' ', $event['target_audience'])),
                'approval_status' => ucfirst($event['approval_status']),
                'priority' => ucfirst($event['priority']),
                'is_featured' => $event['is_featured'] ? 'Yes' : 'No',
                'actions' => $this->generateActionButtons($event)
            ];
        }

        return $this->response->setJSON(['data' => $data]);
    }

    /**
     * Show create form
     */
    public function create()
    {
        $data = [
            'title' => 'Create ' . $this->entityName,
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'event_categories' => $this->eventCategoriesModel->getForDropdown(),
            'sessions' => $this->sessionsModel->getForDropdown(),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => base_url($this->routePrefix)],
                ['name' => 'Create', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/create', $data);
    }

    /**
     * Store new event
     */
    public function store()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $data = $this->request->getPost();
        
        // Add current session if not provided
        if (empty($data['session_id'])) {
            helper('session');
            $data['session_id'] = get_current_session_id();
        }

        // Add created_by
        $data['created_by'] = user_id();

        $result = $this->model->createEvent($data);
        return $this->response->setJSON($result);
    }

    /**
     * Show event details
     */
    public function show($id)
    {
        $event = $this->model->getEventsWithDetails(['id' => $id]);
        
        if (empty($event)) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Event not found');
        }

        $event = $event[0];
        
        // Get registration statistics if registration is required
        $registrationStats = null;
        if ($event['registration_required']) {
            $registrationStats = $this->eventRegistrationsModel->getEventRegistrationStats($id);
        }

        $data = [
            'title' => $event['title'],
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'event' => $event,
            'registration_stats' => $registrationStats,
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => base_url($this->routePrefix)],
                ['name' => $event['title'], 'url' => '']
            ]
        ];

        return view($this->viewPath . '/show', $data);
    }

    /**
     * Show edit form
     */
    public function edit($id)
    {
        $event = $this->model->find($id);
        
        if (!$event) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Event not found');
        }

        $data = [
            'title' => 'Edit ' . $this->entityName,
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'event' => $event,
            'event_categories' => $this->eventCategoriesModel->getForDropdown(),
            'sessions' => $this->sessionsModel->getForDropdown(),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => base_url($this->routePrefix)],
                ['name' => 'Edit', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/edit', $data);
    }

    /**
     * Update event
     */
    public function update($id)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $data = $this->request->getPost();
        $result = $this->model->updateEvent($id, $data);
        
        return $this->response->setJSON($result);
    }

    /**
     * Delete event
     */
    public function delete($id)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        if ($this->model->delete($id)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Event deleted successfully'
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to delete event'
        ]);
    }

    /**
     * Approve event
     */
    public function approve($id)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $result = $this->model->approveEvent($id, user_id());
        return $this->response->setJSON($result);
    }

    /**
     * Reject event
     */
    public function reject($id)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $result = $this->model->rejectEvent($id, user_id());
        return $this->response->setJSON($result);
    }

    /**
     * Get events for calendar display
     */
    public function getCalendarEvents()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $start = $this->request->getGet('start');
        $end = $this->request->getGet('end');
        $sessionId = $this->request->getGet('session_id') ?: session()->get('current_session_id');

        $events = $this->model->getCalendarEvents([
            'session_id' => $sessionId,
            'start_date' => $start,
            'end_date' => $end,
            'approval_status' => 'approved'
        ]);

        return $this->response->setJSON($events);
    }

    /**
     * Generate action buttons for DataTable
     */
    private function generateActionButtons($event)
    {
        $buttons = [];
        
        // View button
        $buttons[] = '<a href="' . base_url($this->routePrefix . '/' . $event['id']) . '" 
                        class="btn btn-sm btn-info" title="View">
                        <i class="fas fa-eye"></i>
                      </a>';
        
        // Edit button
        $buttons[] = '<a href="' . base_url($this->routePrefix . '/' . $event['id'] . '/edit') . '" 
                        class="btn btn-sm btn-warning" title="Edit">
                        <i class="fas fa-edit"></i>
                      </a>';
        
        // Approval buttons
        if ($event['approval_status'] === 'pending') {
            $buttons[] = '<button onclick="approveEvent(' . $event['id'] . ')" 
                            class="btn btn-sm btn-success" title="Approve">
                            <i class="fas fa-check"></i>
                          </button>';
            
            $buttons[] = '<button onclick="rejectEvent(' . $event['id'] . ')" 
                            class="btn btn-sm btn-danger" title="Reject">
                            <i class="fas fa-times"></i>
                          </button>';
        }
        
        // Delete button
        $buttons[] = '<button onclick="deleteEvent(' . $event['id'] . ')" 
                        class="btn btn-sm btn-danger" title="Delete">
                        <i class="fas fa-trash"></i>
                      </button>';
        
        return implode(' ', $buttons);
    }
}
