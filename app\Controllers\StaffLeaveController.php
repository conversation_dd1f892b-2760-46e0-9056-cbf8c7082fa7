<?php

namespace App\Controllers;

use App\Models\StaffLeaveRequestModel;
use App\Models\StaffLeaveTypesModel;
use App\Models\StaffModel;
use App\Models\DepartmentModel;

class StaffLeaveController extends BaseCrudController
{
    protected $staffLeaveTypesModel;
    protected $staffModel;
    protected $departmentModel;

    public function __construct()
    {
        parent::__construct();
        $this->model = new StaffLeaveRequestModel();
        $this->staffLeaveTypesModel = new StaffLeaveTypesModel();
        $this->staffModel = new StaffModel();
        $this->departmentModel = new DepartmentModel();
        
        $this->viewPath = 'admin/staff_leave';
        $this->routePrefix = 'admin/staff-leave';
        $this->entityName = 'Staff Leave';
        $this->entityNamePlural = 'Staff Leave Requests';
    }

    /**
     * Display leave requests list
     */
    public function index()
    {
        $data = [
            'title' => 'Staff Leave Requests',
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'staff_list' => $this->staffModel->getForDropdown(),
            'leave_types' => $this->staffLeaveTypesModel->getForDropdown(),
            'departments' => $this->departmentModel->getForDropdown(),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => 'Staff Leave Requests', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/index', $data);
    }

    /**
     * Get leave requests data for DataTables
     */
    public function getData()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $filters = [
            'staff_id' => $this->request->getGet('staff_id'),
            'leave_type_id' => $this->request->getGet('leave_type_id'),
            'status' => $this->request->getGet('status'),
            'department_id' => $this->request->getGet('department_id'),
            'year' => $this->request->getGet('year')
        ];

        $leaveRequests = $this->model->getLeaveRequestsWithDetails($filters);

        $data = [];
        foreach ($leaveRequests as $request) {
            $statusBadge = $this->getStatusBadge($request['status']);
            
            $data[] = [
                'id' => $request['id'],
                'staff_name' => $request['name'] . ' ' . $request['surname'],
                'employee_id' => $request['employee_id'],
                'department' => $request['department_name'] ?? 'Not assigned',
                'leave_type' => $request['leave_type'],
                'apply_date' => date('M d, Y', strtotime($request['apply_date'])),
                'leave_from' => date('M d, Y', strtotime($request['leave_from'])),
                'leave_to' => date('M d, Y', strtotime($request['leave_to'])),
                'leave_days' => $request['leave_days'],
                'status' => $statusBadge,
                'actions' => $this->generateActionButtons($request)
            ];
        }

        return $this->response->setJSON(['data' => $data]);
    }

    /**
     * Show create form
     */
    public function create()
    {
        $data = [
            'title' => 'Apply for Leave',
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'staff_list' => $this->staffModel->getForDropdown(),
            'leave_types' => $this->staffLeaveTypesModel->getActive(),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => base_url($this->routePrefix)],
                ['name' => 'Apply for Leave', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/create', $data);
    }

    /**
     * Store leave request
     */
    public function store()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $data = $this->request->getPost();
        $result = $this->model->applyLeave($data);
        
        return $this->response->setJSON($result);
    }

    /**
     * Show leave request details
     */
    public function show($id)
    {
        $leaveRequest = $this->model->getLeaveRequestsWithDetails(['id' => $id]);
        
        if (empty($leaveRequest)) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Leave request not found');
        }

        $leaveRequest = $leaveRequest[0];

        $data = [
            'title' => 'Leave Request Details',
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'leave_request' => $leaveRequest,
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => base_url($this->routePrefix)],
                ['name' => 'Details', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/show', $data);
    }

    /**
     * Show edit form
     */
    public function edit($id)
    {
        $leaveRequest = $this->model->find($id);
        
        if (!$leaveRequest) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Leave request not found');
        }

        if ($leaveRequest['status'] !== 'pending') {
            return redirect()->back()->with('error', 'Only pending leave requests can be edited');
        }

        $data = [
            'title' => 'Edit Leave Request',
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'leave_request' => $leaveRequest,
            'staff_list' => $this->staffModel->getForDropdown(),
            'leave_types' => $this->staffLeaveTypesModel->getActive(),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => base_url($this->routePrefix)],
                ['name' => 'Edit', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/edit', $data);
    }

    /**
     * Update leave request
     */
    public function update($id)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $data = $this->request->getPost();
        $result = $this->model->updateLeaveRequest($id, $data);
        
        return $this->response->setJSON($result);
    }

    /**
     * Approve leave request
     */
    public function approve($id)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $adminRemark = $this->request->getPost('admin_remark');
        $result = $this->model->approveLeave($id, user_id(), $adminRemark);
        
        return $this->response->setJSON($result);
    }

    /**
     * Disapprove leave request
     */
    public function disapprove($id)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $adminRemark = $this->request->getPost('admin_remark');
        $result = $this->model->disapproveLeave($id, user_id(), $adminRemark);
        
        return $this->response->setJSON($result);
    }

    /**
     * Cancel leave request
     */
    public function cancel($id)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $reason = $this->request->getPost('reason');
        $result = $this->model->cancelLeave($id, $reason);
        
        return $this->response->setJSON($result);
    }

    /**
     * Show pending requests
     */
    public function pending()
    {
        $departmentId = $this->request->getGet('department_id');
        
        $data = [
            'title' => 'Pending Leave Requests',
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'departments' => $this->departmentModel->getForDropdown(),
            'pending_requests' => $this->model->getPendingRequests($departmentId),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => base_url($this->routePrefix)],
                ['name' => 'Pending Requests', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/pending', $data);
    }

    /**
     * Show leave report
     */
    public function report()
    {
        $month = $this->request->getGet('month') ?: date('m');
        $year = $this->request->getGet('year') ?: date('Y');
        $departmentId = $this->request->getGet('department_id');

        $data = [
            'title' => 'Staff Leave Report',
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'month' => $month,
            'year' => $year,
            'department_id' => $departmentId,
            'departments' => $this->departmentModel->getForDropdown(),
            'report_data' => $this->model->getMonthlyLeaveReport($month, $year, $departmentId),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => base_url($this->routePrefix)],
                ['name' => 'Report', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/report', $data);
    }

    /**
     * Get leave balance for staff
     */
    public function getLeaveBalance()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $staffId = $this->request->getGet('staff_id');
        $year = $this->request->getGet('year') ?: date('Y');

        if (!$staffId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Staff ID is required'
            ]);
        }

        $balances = $this->staffLeaveTypesModel->getAllLeaveBalances($staffId, $year);

        return $this->response->setJSON([
            'success' => true,
            'data' => $balances
        ]);
    }

    /**
     * Get leave statistics
     */
    public function getStatistics()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $filters = [
            'year' => $this->request->getGet('year'),
            'department_id' => $this->request->getGet('department_id')
        ];

        $stats = $this->model->getStatistics($filters);
        return $this->response->setJSON($stats);
    }

    /**
     * Get staff on leave for a date
     */
    public function getStaffOnLeave()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $date = $this->request->getGet('date') ?: date('Y-m-d');
        $staffOnLeave = $this->model->getStaffOnLeave($date);

        return $this->response->setJSON([
            'success' => true,
            'data' => $staffOnLeave,
            'date' => $date
        ]);
    }

    /**
     * Generate status badge
     */
    private function getStatusBadge($status)
    {
        switch ($status) {
            case 'pending':
                return '<span class="badge badge-warning">Pending</span>';
            case 'approved':
                return '<span class="badge badge-success">Approved</span>';
            case 'disapproved':
                return '<span class="badge badge-danger">Disapproved</span>';
            default:
                return '<span class="badge badge-secondary">' . ucfirst($status) . '</span>';
        }
    }

    /**
     * Generate action buttons for DataTable
     */
    private function generateActionButtons($request)
    {
        $buttons = [];
        
        // View button
        $buttons[] = '<a href="' . base_url($this->routePrefix . '/' . $request['id']) . '" 
                        class="btn btn-sm btn-info" title="View">
                        <i class="fas fa-eye"></i>
                      </a>';
        
        // Edit button (only for pending requests)
        if ($request['status'] === 'pending') {
            $buttons[] = '<a href="' . base_url($this->routePrefix . '/edit/' . $request['id']) . '" 
                            class="btn btn-sm btn-warning" title="Edit">
                            <i class="fas fa-edit"></i>
                          </a>';
        }
        
        // Approval buttons (only for pending requests)
        if ($request['status'] === 'pending') {
            $buttons[] = '<button onclick="approveLeave(' . $request['id'] . ')" 
                            class="btn btn-sm btn-success" title="Approve">
                            <i class="fas fa-check"></i>
                          </button>';
            
            $buttons[] = '<button onclick="disapproveLeave(' . $request['id'] . ')" 
                            class="btn btn-sm btn-danger" title="Disapprove">
                            <i class="fas fa-times"></i>
                          </button>';
        }
        
        // Cancel button (only for pending requests)
        if ($request['status'] === 'pending') {
            $buttons[] = '<button onclick="cancelLeave(' . $request['id'] . ')" 
                            class="btn btn-sm btn-secondary" title="Cancel">
                            <i class="fas fa-ban"></i>
                          </button>';
        }
        
        return implode(' ', $buttons);
    }
}
