<?php

namespace App\Models;

class StaffPayrollModel extends BaseModel
{
    protected $table = 'staff_payroll';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'basic_salary', 'pay_scale', 'grade', 'is_active'
    ];

    protected $validationRules = [
        'basic_salary' => 'required|numeric|greater_than[0]',
        'pay_scale' => 'required|min_length[2]|max_length[200]',
        'grade' => 'required|min_length[1]|max_length[50]'
    ];

    protected $validationMessages = [
        'basic_salary' => [
            'required' => 'Basic salary is required',
            'numeric' => 'Basic salary must be a number',
            'greater_than' => 'Basic salary must be greater than 0'
        ],
        'pay_scale' => [
            'required' => 'Pay scale is required',
            'min_length' => 'Pay scale must be at least 2 characters long',
            'max_length' => 'Pay scale cannot exceed 200 characters'
        ],
        'grade' => [
            'required' => 'Grade is required',
            'min_length' => 'Grade must be at least 1 character long',
            'max_length' => 'Grade cannot exceed 50 characters'
        ]
    ];

    protected $searchableColumns = ['pay_scale', 'grade'];
    protected $orderableColumns = ['id', 'basic_salary', 'pay_scale', 'grade', 'is_active'];

    /**
     * Get active payroll scales
     */
    public function getActive()
    {
        return $this->where('is_active', 'yes')
                   ->orderBy('pay_scale', 'ASC')
                   ->findAll();
    }

    /**
     * Get payroll scales for dropdown
     */
    public function getForDropdown()
    {
        $payrolls = $this->getActive();
        
        $dropdown = [];
        foreach ($payrolls as $payroll) {
            $dropdown[$payroll['id']] = $payroll['pay_scale'] . ' - Grade ' . $payroll['grade'];
        }
        
        return $dropdown;
    }

    /**
     * Get payroll by grade
     */
    public function getByGrade($grade)
    {
        return $this->where('grade', $grade)
                   ->where('is_active', 'yes')
                   ->orderBy('basic_salary', 'ASC')
                   ->findAll();
    }

    /**
     * Get payroll statistics
     */
    public function getStatistics()
    {
        $stats = [];
        
        // Total payroll scales
        $stats['total'] = $this->countAllResults();
        
        // Active payroll scales
        $stats['active'] = $this->where('is_active', 'yes')->countAllResults();
        
        // Salary range
        $salaryStats = $this->selectMin('basic_salary', 'min_salary')
                           ->selectMax('basic_salary', 'max_salary')
                           ->selectAvg('basic_salary', 'avg_salary')
                           ->where('is_active', 'yes')
                           ->first();
        
        $stats['salary_range'] = [
            'min' => $salaryStats['min_salary'] ?? 0,
            'max' => $salaryStats['max_salary'] ?? 0,
            'avg' => round($salaryStats['avg_salary'] ?? 0, 2)
        ];
        
        // Payroll usage by staff
        $stats['usage'] = $this->db->table($this->table)
                                  ->select('staff_payroll.pay_scale, staff_payroll.grade, COUNT(staff.id) as staff_count')
                                  ->join('staff', 'staff_payroll.id = staff.payscale', 'left')
                                  ->where('staff_payroll.is_active', 'yes')
                                  ->groupBy('staff_payroll.id')
                                  ->orderBy('staff_count', 'DESC')
                                  ->get()
                                  ->getResultArray();
        
        return $stats;
    }

    /**
     * Create payroll scale with validation
     */
    public function createPayrollScale($data)
    {
        // Set defaults
        $data['is_active'] = $data['is_active'] ?? 'yes';

        // Check for duplicate pay scale and grade combination
        $existing = $this->where('pay_scale', $data['pay_scale'])
                        ->where('grade', $data['grade'])
                        ->first();

        if ($existing) {
            return [
                'success' => false,
                'message' => 'Pay scale with this grade already exists'
            ];
        }

        if ($this->insert($data)) {
            return [
                'success' => true,
                'message' => 'Payroll scale created successfully',
                'data' => $this->find($this->getInsertID())
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to create payroll scale',
            'errors' => $this->errors()
        ];
    }

    /**
     * Update payroll scale with validation
     */
    public function updatePayrollScale($id, $data)
    {
        // Check for duplicate pay scale and grade combination (excluding current record)
        $existing = $this->where('pay_scale', $data['pay_scale'])
                        ->where('grade', $data['grade'])
                        ->where('id !=', $id)
                        ->first();

        if ($existing) {
            return [
                'success' => false,
                'message' => 'Pay scale with this grade already exists'
            ];
        }

        if ($this->update($id, $data)) {
            return [
                'success' => true,
                'message' => 'Payroll scale updated successfully',
                'data' => $this->find($id)
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to update payroll scale',
            'errors' => $this->errors()
        ];
    }

    /**
     * Delete payroll scale with validation
     */
    public function deletePayrollScale($id)
    {
        // Check if payroll scale is being used by staff
        $usageCount = $this->db->table('staff')
                              ->where('payscale', $id)
                              ->countAllResults();

        if ($usageCount > 0) {
            return [
                'success' => false,
                'message' => 'Cannot delete payroll scale. It is being used by staff members.'
            ];
        }

        if ($this->delete($id)) {
            return [
                'success' => true,
                'message' => 'Payroll scale deleted successfully'
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to delete payroll scale'
        ];
    }

    /**
     * Get payroll scales with usage count
     */
    public function getWithUsageCount()
    {
        $builder = $this->db->table($this->table);
        $builder->select('staff_payroll.*, COUNT(staff.id) as usage_count')
                ->join('staff', 'staff_payroll.id = staff.payscale', 'left')
                ->groupBy('staff_payroll.id')
                ->orderBy('staff_payroll.pay_scale', 'ASC');

        return $builder->get()->getResultArray();
    }

    /**
     * Check if payroll scale can be deleted
     */
    public function canDelete($id)
    {
        $usageCount = $this->db->table('staff')
                              ->where('payscale', $id)
                              ->countAllResults();

        return $usageCount === 0;
    }

    /**
     * Get default payroll scales for seeding
     */
    public function getDefaultPayrollScales()
    {
        return [
            [
                'basic_salary' => 15000,
                'pay_scale' => 'Teaching Staff - Primary',
                'grade' => 'T1',
                'is_active' => 'yes'
            ],
            [
                'basic_salary' => 18000,
                'pay_scale' => 'Teaching Staff - Secondary',
                'grade' => 'T2',
                'is_active' => 'yes'
            ],
            [
                'basic_salary' => 22000,
                'pay_scale' => 'Teaching Staff - Senior Secondary',
                'grade' => 'T3',
                'is_active' => 'yes'
            ],
            [
                'basic_salary' => 25000,
                'pay_scale' => 'Teaching Staff - Head Teacher',
                'grade' => 'T4',
                'is_active' => 'yes'
            ],
            [
                'basic_salary' => 12000,
                'pay_scale' => 'Non-Teaching Staff - Grade IV',
                'grade' => 'NT1',
                'is_active' => 'yes'
            ],
            [
                'basic_salary' => 14000,
                'pay_scale' => 'Non-Teaching Staff - Grade III',
                'grade' => 'NT2',
                'is_active' => 'yes'
            ],
            [
                'basic_salary' => 16000,
                'pay_scale' => 'Non-Teaching Staff - Grade II',
                'grade' => 'NT3',
                'is_active' => 'yes'
            ],
            [
                'basic_salary' => 20000,
                'pay_scale' => 'Non-Teaching Staff - Grade I',
                'grade' => 'NT4',
                'is_active' => 'yes'
            ],
            [
                'basic_salary' => 30000,
                'pay_scale' => 'Administrative Staff - Officer',
                'grade' => 'A1',
                'is_active' => 'yes'
            ],
            [
                'basic_salary' => 35000,
                'pay_scale' => 'Administrative Staff - Senior Officer',
                'grade' => 'A2',
                'is_active' => 'yes'
            ]
        ];
    }

    /**
     * Bulk create default payroll scales
     */
    public function createDefaultPayrollScales()
    {
        $defaultScales = $this->getDefaultPayrollScales();
        $createdCount = 0;

        foreach ($defaultScales as $scale) {
            // Check if scale already exists
            $existing = $this->where('pay_scale', $scale['pay_scale'])
                            ->where('grade', $scale['grade'])
                            ->first();
            
            if (!$existing) {
                if ($this->insert($scale)) {
                    $createdCount++;
                }
            }
        }

        return [
            'success' => true,
            'created_count' => $createdCount,
            'message' => "Created {$createdCount} default payroll scales"
        ];
    }

    /**
     * Get salary breakdown for a payroll scale
     */
    public function getSalaryBreakdown($payrollId, $allowances = [], $deductions = [])
    {
        $payroll = $this->find($payrollId);
        
        if (!$payroll) {
            return null;
        }

        $basicSalary = $payroll['basic_salary'];
        $totalAllowances = array_sum($allowances);
        $totalDeductions = array_sum($deductions);
        $grossSalary = $basicSalary + $totalAllowances;
        $netSalary = $grossSalary - $totalDeductions;

        return [
            'payroll_info' => $payroll,
            'basic_salary' => $basicSalary,
            'allowances' => $allowances,
            'total_allowances' => $totalAllowances,
            'gross_salary' => $grossSalary,
            'deductions' => $deductions,
            'total_deductions' => $totalDeductions,
            'net_salary' => $netSalary
        ];
    }
}
