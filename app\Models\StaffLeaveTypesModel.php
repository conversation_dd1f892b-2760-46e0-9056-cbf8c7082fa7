<?php

namespace App\Models;

class StaffLeaveTypesModel extends BaseModel
{
    protected $table = 'staff_leave_types';
    protected $primaryKey = 'id';
    protected $allowedFields = ['type', 'max_leave', 'is_active'];

    protected $validationRules = [
        'type' => 'required|min_length[2]|max_length[200]|is_unique[staff_leave_types.type,id,{id}]',
        'max_leave' => 'required|integer|greater_than_equal_to[0]'
    ];

    protected $validationMessages = [
        'type' => [
            'required' => 'Leave type is required',
            'min_length' => 'Leave type must be at least 2 characters long',
            'max_length' => 'Leave type cannot exceed 200 characters',
            'is_unique' => 'Leave type already exists'
        ],
        'max_leave' => [
            'required' => 'Maximum leave days is required',
            'integer' => 'Maximum leave days must be a number',
            'greater_than_equal_to' => 'Maximum leave days cannot be negative'
        ]
    ];

    protected $searchableColumns = ['type'];
    protected $orderableColumns = ['id', 'type', 'max_leave', 'is_active'];

    /**
     * Get active leave types
     */
    public function getActive()
    {
        return $this->where('is_active', 'yes')
                   ->orderBy('type', 'ASC')
                   ->findAll();
    }

    /**
     * Get leave types for dropdown
     */
    public function getForDropdown()
    {
        $types = $this->getActive();
        
        $dropdown = [];
        foreach ($types as $type) {
            $dropdown[$type['id']] = $type['type'] . ' (Max: ' . $type['max_leave'] . ' days)';
        }
        
        return $dropdown;
    }

    /**
     * Get leave type statistics
     */
    public function getStatistics()
    {
        $stats = [];
        
        // Total leave types
        $stats['total'] = $this->countAllResults();
        
        // Active leave types
        $stats['active'] = $this->where('is_active', 'yes')->countAllResults();
        
        // Total max leave days
        $maxLeaveStats = $this->selectSum('max_leave', 'total_max_leave')
                             ->where('is_active', 'yes')
                             ->first();
        
        $stats['total_max_leave'] = $maxLeaveStats['total_max_leave'] ?? 0;
        
        // Usage statistics
        $stats['usage'] = $this->db->table($this->table)
                                  ->select('staff_leave_types.type, COUNT(staff_leave_request.id) as usage_count')
                                  ->join('staff_leave_request', 'staff_leave_types.id = staff_leave_request.leave_type_id', 'left')
                                  ->where('staff_leave_types.is_active', 'yes')
                                  ->groupBy('staff_leave_types.id')
                                  ->orderBy('usage_count', 'DESC')
                                  ->get()
                                  ->getResultArray();
        
        return $stats;
    }

    /**
     * Create leave type with validation
     */
    public function createLeaveType($data)
    {
        // Set defaults
        $data['is_active'] = $data['is_active'] ?? 'yes';

        if ($this->insert($data)) {
            return [
                'success' => true,
                'message' => 'Leave type created successfully',
                'data' => $this->find($this->getInsertID())
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to create leave type',
            'errors' => $this->errors()
        ];
    }

    /**
     * Update leave type with validation
     */
    public function updateLeaveType($id, $data)
    {
        if ($this->update($id, $data)) {
            return [
                'success' => true,
                'message' => 'Leave type updated successfully',
                'data' => $this->find($id)
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to update leave type',
            'errors' => $this->errors()
        ];
    }

    /**
     * Delete leave type with validation
     */
    public function deleteLeaveType($id)
    {
        // Check if leave type is being used
        $usageCount = $this->db->table('staff_leave_request')
                              ->where('leave_type_id', $id)
                              ->countAllResults();

        if ($usageCount > 0) {
            return [
                'success' => false,
                'message' => 'Cannot delete leave type. It is being used in leave requests.'
            ];
        }

        if ($this->delete($id)) {
            return [
                'success' => true,
                'message' => 'Leave type deleted successfully'
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to delete leave type'
        ];
    }

    /**
     * Get leave types with usage count
     */
    public function getWithUsageCount()
    {
        $builder = $this->db->table($this->table);
        $builder->select('staff_leave_types.*, COUNT(staff_leave_request.id) as usage_count')
                ->join('staff_leave_request', 'staff_leave_types.id = staff_leave_request.leave_type_id', 'left')
                ->groupBy('staff_leave_types.id')
                ->orderBy('staff_leave_types.type', 'ASC');

        return $builder->get()->getResultArray();
    }

    /**
     * Check if leave type can be deleted
     */
    public function canDelete($id)
    {
        $usageCount = $this->db->table('staff_leave_request')
                              ->where('leave_type_id', $id)
                              ->countAllResults();

        return $usageCount === 0;
    }

    /**
     * Get default leave types for seeding
     */
    public function getDefaultLeaveTypes()
    {
        return [
            [
                'type' => 'Casual Leave',
                'max_leave' => 12,
                'is_active' => 'yes'
            ],
            [
                'type' => 'Medical Leave',
                'max_leave' => 15,
                'is_active' => 'yes'
            ],
            [
                'type' => 'Earned Leave',
                'max_leave' => 21,
                'is_active' => 'yes'
            ],
            [
                'type' => 'Maternity Leave',
                'max_leave' => 180,
                'is_active' => 'yes'
            ],
            [
                'type' => 'Paternity Leave',
                'max_leave' => 15,
                'is_active' => 'yes'
            ],
            [
                'type' => 'Emergency Leave',
                'max_leave' => 5,
                'is_active' => 'yes'
            ],
            [
                'type' => 'Study Leave',
                'max_leave' => 30,
                'is_active' => 'yes'
            ]
        ];
    }

    /**
     * Bulk create default leave types
     */
    public function createDefaultLeaveTypes()
    {
        $defaultTypes = $this->getDefaultLeaveTypes();
        $createdCount = 0;

        foreach ($defaultTypes as $type) {
            // Check if type already exists
            $existing = $this->where('type', $type['type'])->first();
            
            if (!$existing) {
                if ($this->insert($type)) {
                    $createdCount++;
                }
            }
        }

        return [
            'success' => true,
            'created_count' => $createdCount,
            'message' => "Created {$createdCount} default leave types"
        ];
    }

    /**
     * Get leave balance for staff member
     */
    public function getLeaveBalance($staffId, $leaveTypeId, $year = null)
    {
        $year = $year ?: date('Y');
        
        // Get leave type details
        $leaveType = $this->find($leaveTypeId);
        if (!$leaveType) {
            return null;
        }

        // Calculate used leave days for the year
        $usedDays = $this->db->table('staff_leave_request')
                            ->selectSum('leave_days', 'total_used')
                            ->where('staff_id', $staffId)
                            ->where('leave_type_id', $leaveTypeId)
                            ->where('status', 'approved')
                            ->where('YEAR(leave_from)', $year)
                            ->get()
                            ->getRowArray();

        $totalUsed = $usedDays['total_used'] ?? 0;
        $balance = $leaveType['max_leave'] - $totalUsed;

        return [
            'leave_type' => $leaveType['type'],
            'max_leave' => $leaveType['max_leave'],
            'used_leave' => $totalUsed,
            'balance' => $balance,
            'year' => $year
        ];
    }

    /**
     * Get all leave balances for staff member
     */
    public function getAllLeaveBalances($staffId, $year = null)
    {
        $year = $year ?: date('Y');
        $leaveTypes = $this->getActive();
        
        $balances = [];
        foreach ($leaveTypes as $leaveType) {
            $balance = $this->getLeaveBalance($staffId, $leaveType['id'], $year);
            if ($balance) {
                $balances[] = $balance;
            }
        }

        return $balances;
    }
}
