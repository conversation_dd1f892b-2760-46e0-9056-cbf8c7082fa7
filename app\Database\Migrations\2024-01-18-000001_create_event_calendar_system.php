<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateEventCalendarSystem extends Migration
{
    public function up()
    {
        // Create event_categories table
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
                'comment' => 'Event category name'
            ],
            'description' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Category description'
            ],
            'color' => [
                'type' => 'VARCHAR',
                'constraint' => 7,
                'null' => false,
                'default' => '#007bff',
                'comment' => 'Hex color code for display'
            ],
            'icon' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
                'comment' => 'Font Awesome icon class'
            ],
            'requires_approval' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'null' => false,
                'default' => 0,
                'comment' => '1 if events in this category require approval'
            ],
            'is_active' => [
                'type' => 'VARCHAR',
                'constraint' => 3,
                'null' => false,
                'default' => 'yes'
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true
            ]
        ]);

        $this->forge->addPrimaryKey('id');
        $this->forge->addKey('name');
        $this->forge->addKey('is_active');
        $this->forge->createTable('event_categories');

        // Create school_events table
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true
            ],
            'session_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => false,
                'comment' => 'Academic session'
            ],
            'category_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'comment' => 'Event category'
            ],
            'title' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'null' => false,
                'comment' => 'Event title'
            ],
            'description' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Event description'
            ],
            'event_type' => [
                'type' => 'ENUM',
                'constraint' => ['academic', 'extracurricular', 'administrative', 'sports', 'cultural', 'meeting', 'announcement'],
                'null' => false,
                'default' => 'academic',
                'comment' => 'Type of event'
            ],
            'start_date' => [
                'type' => 'DATE',
                'null' => false,
                'comment' => 'Event start date'
            ],
            'end_date' => [
                'type' => 'DATE',
                'null' => false,
                'comment' => 'Event end date'
            ],
            'start_time' => [
                'type' => 'TIME',
                'null' => true,
                'comment' => 'Event start time'
            ],
            'end_time' => [
                'type' => 'TIME',
                'null' => true,
                'comment' => 'Event end time'
            ],
            'location' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'null' => true,
                'comment' => 'Event location'
            ],
            'organizer_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
                'comment' => 'Event organizer user ID'
            ],
            'target_audience' => [
                'type' => 'ENUM',
                'constraint' => ['all', 'students', 'staff', 'parents', 'specific_classes', 'specific_users'],
                'null' => false,
                'default' => 'all',
                'comment' => 'Who can see/participate in this event'
            ],
            'class_ids' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => 'Array of class IDs for specific_classes target'
            ],
            'user_ids' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => 'Array of user IDs for specific_users target'
            ],
            'max_participants' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
                'comment' => 'Maximum number of participants (null = unlimited)'
            ],
            'registration_required' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'null' => false,
                'default' => 0,
                'comment' => '1 if registration is required'
            ],
            'registration_deadline' => [
                'type' => 'DATE',
                'null' => true,
                'comment' => 'Last date for registration'
            ],
            'approval_status' => [
                'type' => 'ENUM',
                'constraint' => ['pending', 'approved', 'rejected'],
                'null' => false,
                'default' => 'approved',
                'comment' => 'Event approval status'
            ],
            'approved_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
                'comment' => 'User who approved the event'
            ],
            'approved_at' => [
                'type' => 'DATETIME',
                'null' => true,
                'comment' => 'When the event was approved'
            ],
            'priority' => [
                'type' => 'ENUM',
                'constraint' => ['low', 'medium', 'high', 'urgent'],
                'null' => false,
                'default' => 'medium',
                'comment' => 'Event priority level'
            ],
            'is_featured' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'null' => false,
                'default' => 0,
                'comment' => '1 if event should be featured'
            ],
            'attachments' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => 'Array of file attachments'
            ],
            'external_link' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'null' => true,
                'comment' => 'External link related to event'
            ],
            'reminder_sent' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'null' => false,
                'default' => 0,
                'comment' => '1 if reminder notification has been sent'
            ],
            'is_active' => [
                'type' => 'VARCHAR',
                'constraint' => 3,
                'null' => false,
                'default' => 'yes'
            ],
            'created_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => false,
                'comment' => 'User who created the event'
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true
            ]
        ]);

        $this->forge->addPrimaryKey('id');
        $this->forge->addKey('session_id');
        $this->forge->addKey('category_id');
        $this->forge->addKey(['start_date', 'end_date']);
        $this->forge->addKey('event_type');
        $this->forge->addKey('target_audience');
        $this->forge->addKey('approval_status');
        $this->forge->addKey('is_active');
        
        $this->forge->addForeignKey('session_id', 'sessions', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('category_id', 'event_categories', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('organizer_id', 'users', 'id', 'SET NULL', 'SET NULL');
        $this->forge->addForeignKey('approved_by', 'users', 'id', 'SET NULL', 'SET NULL');
        $this->forge->addForeignKey('created_by', 'users', 'id', 'CASCADE', 'CASCADE');
        
        $this->forge->createTable('school_events');

        // Create event_registrations table
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true
            ],
            'event_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'comment' => 'School event ID'
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => false,
                'comment' => 'Registered user ID'
            ],
            'registration_date' => [
                'type' => 'DATETIME',
                'null' => false,
                'comment' => 'When user registered'
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['registered', 'attended', 'cancelled', 'no_show'],
                'null' => false,
                'default' => 'registered',
                'comment' => 'Registration status'
            ],
            'notes' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Additional notes'
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true
            ]
        ]);

        $this->forge->addPrimaryKey('id');
        $this->forge->addKey('event_id');
        $this->forge->addKey('user_id');
        $this->forge->addKey('status');
        $this->forge->addUniqueKey(['event_id', 'user_id'], 'unique_registration');
        
        $this->forge->addForeignKey('event_id', 'school_events', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'CASCADE');
        
        $this->forge->createTable('event_registrations');

        // Insert default event categories
        $this->insertDefaultEventCategories();
    }

    public function down()
    {
        $this->forge->dropTable('event_registrations');
        $this->forge->dropTable('school_events');
        $this->forge->dropTable('event_categories');
    }

    /**
     * Insert default event categories
     */
    private function insertDefaultEventCategories()
    {
        $db = \Config\Database::connect();
        
        $categories = [
            [
                'name' => 'Academic Events',
                'description' => 'Academic activities, exams, orientations',
                'color' => '#007bff',
                'icon' => 'fas fa-graduation-cap',
                'requires_approval' => 0
            ],
            [
                'name' => 'Sports Events',
                'description' => 'Sports competitions and physical activities',
                'color' => '#28a745',
                'icon' => 'fas fa-futbol',
                'requires_approval' => 0
            ],
            [
                'name' => 'Cultural Events',
                'description' => 'Cultural programs, festivals, celebrations',
                'color' => '#e83e8c',
                'icon' => 'fas fa-theater-masks',
                'requires_approval' => 0
            ],
            [
                'name' => 'Administrative',
                'description' => 'Staff meetings, administrative activities',
                'color' => '#6c757d',
                'icon' => 'fas fa-users-cog',
                'requires_approval' => 1
            ],
            [
                'name' => 'Parent Meetings',
                'description' => 'Parent-teacher meetings, conferences',
                'color' => '#fd7e14',
                'icon' => 'fas fa-handshake',
                'requires_approval' => 0
            ],
            [
                'name' => 'Extracurricular',
                'description' => 'Clubs, societies, hobby activities',
                'color' => '#20c997',
                'icon' => 'fas fa-puzzle-piece',
                'requires_approval' => 0
            ],
            [
                'name' => 'Announcements',
                'description' => 'Important announcements and notices',
                'color' => '#ffc107',
                'icon' => 'fas fa-bullhorn',
                'requires_approval' => 1
            ]
        ];

        foreach ($categories as $category) {
            $category['is_active'] = 'yes';
            $category['created_at'] = date('Y-m-d H:i:s');
            $category['updated_at'] = date('Y-m-d H:i:s');
            
            $db->table('event_categories')->insert($category);
        }
    }
}
