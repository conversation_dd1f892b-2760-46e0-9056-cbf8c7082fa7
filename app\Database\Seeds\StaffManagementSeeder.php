<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class StaffManagementSeeder extends Seeder
{
    public function run()
    {
        // Seed staff attendance types
        $this->seedStaffAttendanceTypes();
        
        // Seed staff leave types
        $this->seedStaffLeaveTypes();
        
        // Seed staff payroll scales
        $this->seedStaffPayrollScales();
        
        echo "Staff Management system seeded successfully!\n";
    }

    private function seedStaffAttendanceTypes()
    {
        $attendanceTypes = [
            [
                'type' => 'Present',
                'key_value' => 'P',
                'is_active' => 'yes',
                'for_qr_attendance' => 1,
                'long_lang_name' => 'Present',
                'long_name_style' => 'color: green;'
            ],
            [
                'type' => 'Absent',
                'key_value' => 'A',
                'is_active' => 'yes',
                'for_qr_attendance' => 1,
                'long_lang_name' => 'Absent',
                'long_name_style' => 'color: red;'
            ],
            [
                'type' => 'Late',
                'key_value' => 'L',
                'is_active' => 'yes',
                'for_qr_attendance' => 1,
                'long_lang_name' => 'Late',
                'long_name_style' => 'color: orange;'
            ],
            [
                'type' => 'Half Day',
                'key_value' => 'H',
                'is_active' => 'yes',
                'for_qr_attendance' => 1,
                'long_lang_name' => 'Half Day',
                'long_name_style' => 'color: blue;'
            ],
            [
                'type' => 'Permission',
                'key_value' => 'PR',
                'is_active' => 'yes',
                'for_qr_attendance' => 1,
                'long_lang_name' => 'Permission',
                'long_name_style' => 'color: purple;'
            ],
            [
                'type' => 'Medical Leave',
                'key_value' => 'ML',
                'is_active' => 'yes',
                'for_qr_attendance' => 0,
                'long_lang_name' => 'Medical Leave',
                'long_name_style' => 'color: teal;'
            ],
            [
                'type' => 'Casual Leave',
                'key_value' => 'CL',
                'is_active' => 'yes',
                'for_qr_attendance' => 0,
                'long_lang_name' => 'Casual Leave',
                'long_name_style' => 'color: indigo;'
            ]
        ];

        foreach ($attendanceTypes as $type) {
            // Check if type already exists
            $existing = $this->db->table('staff_attendance_type')
                                ->where('key_value', $type['key_value'])
                                ->get()
                                ->getRowArray();
            
            if (!$existing) {
                $this->db->table('staff_attendance_type')->insert($type);
                echo "Created staff attendance type: {$type['type']}\n";
            }
        }
    }

    private function seedStaffLeaveTypes()
    {
        $leaveTypes = [
            [
                'type' => 'Casual Leave',
                'max_leave' => 12,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'type' => 'Medical Leave',
                'max_leave' => 15,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'type' => 'Earned Leave',
                'max_leave' => 21,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'type' => 'Maternity Leave',
                'max_leave' => 180,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'type' => 'Paternity Leave',
                'max_leave' => 15,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'type' => 'Emergency Leave',
                'max_leave' => 5,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'type' => 'Study Leave',
                'max_leave' => 30,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        foreach ($leaveTypes as $leaveType) {
            // Check if type already exists
            $existing = $this->db->table('staff_leave_types')
                                ->where('type', $leaveType['type'])
                                ->get()
                                ->getRowArray();
            
            if (!$existing) {
                $this->db->table('staff_leave_types')->insert($leaveType);
                echo "Created staff leave type: {$leaveType['type']}\n";
            }
        }
    }

    private function seedStaffPayrollScales()
    {
        $payrollScales = [
            [
                'basic_salary' => 15000,
                'pay_scale' => 'Teaching Staff - Primary',
                'grade' => 'T1',
                'is_active' => 'yes'
            ],
            [
                'basic_salary' => 18000,
                'pay_scale' => 'Teaching Staff - Secondary',
                'grade' => 'T2',
                'is_active' => 'yes'
            ],
            [
                'basic_salary' => 22000,
                'pay_scale' => 'Teaching Staff - Senior Secondary',
                'grade' => 'T3',
                'is_active' => 'yes'
            ],
            [
                'basic_salary' => 25000,
                'pay_scale' => 'Teaching Staff - Head Teacher',
                'grade' => 'T4',
                'is_active' => 'yes'
            ],
            [
                'basic_salary' => 12000,
                'pay_scale' => 'Non-Teaching Staff - Grade IV',
                'grade' => 'NT1',
                'is_active' => 'yes'
            ],
            [
                'basic_salary' => 14000,
                'pay_scale' => 'Non-Teaching Staff - Grade III',
                'grade' => 'NT2',
                'is_active' => 'yes'
            ],
            [
                'basic_salary' => 16000,
                'pay_scale' => 'Non-Teaching Staff - Grade II',
                'grade' => 'NT3',
                'is_active' => 'yes'
            ],
            [
                'basic_salary' => 20000,
                'pay_scale' => 'Non-Teaching Staff - Grade I',
                'grade' => 'NT4',
                'is_active' => 'yes'
            ],
            [
                'basic_salary' => 30000,
                'pay_scale' => 'Administrative Staff - Officer',
                'grade' => 'A1',
                'is_active' => 'yes'
            ],
            [
                'basic_salary' => 35000,
                'pay_scale' => 'Administrative Staff - Senior Officer',
                'grade' => 'A2',
                'is_active' => 'yes'
            ]
        ];

        foreach ($payrollScales as $scale) {
            // Check if scale already exists
            $existing = $this->db->table('staff_payroll')
                                ->where('pay_scale', $scale['pay_scale'])
                                ->where('grade', $scale['grade'])
                                ->get()
                                ->getRowArray();
            
            if (!$existing) {
                $this->db->table('staff_payroll')->insert($scale);
                echo "Created staff payroll scale: {$scale['pay_scale']} - {$scale['grade']}\n";
            }
        }
    }
}
