<?php

namespace App\Models;

class StaffAttendanceModel extends BaseModel
{
    protected $table = 'staff_attendance';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'date', 'staff_id', 'staff_attendance_type_id', 'biometric_attendence',
        'qrcode_attendance', 'biometric_device_data', 'user_agent', 'remark', 'is_active'
    ];

    protected $validationRules = [
        'date' => 'required|valid_date',
        'staff_id' => 'required|integer',
        'staff_attendance_type_id' => 'required|integer'
    ];

    protected $validationMessages = [
        'date' => [
            'required' => 'Date is required',
            'valid_date' => 'Please enter a valid date'
        ],
        'staff_id' => [
            'required' => 'Staff member is required',
            'integer' => 'Invalid staff ID'
        ],
        'staff_attendance_type_id' => [
            'required' => 'Attendance type is required',
            'integer' => 'Invalid attendance type'
        ]
    ];

    protected $searchableColumns = ['date', 'remark'];
    protected $orderableColumns = ['id', 'date', 'staff_attendance_type_id', 'created_at'];

    // Attendance type constants
    const ATTENDANCE_TYPE_PRESENT = 1;
    const ATTENDANCE_TYPE_ABSENT = 2;
    const ATTENDANCE_TYPE_LATE = 3;
    const ATTENDANCE_TYPE_HALF_DAY = 4;
    const ATTENDANCE_TYPE_PERMISSION = 5;

    /**
     * Get attendance with staff and type details
     */
    public function getAttendanceWithDetails($filters = [])
    {
        $builder = $this->db->table($this->table);
        $builder->select('staff_attendance.*, staff.name, staff.surname, staff.employee_id,
                         staff_attendance_type.type as attendance_type')
                ->join('staff', 'staff_attendance.staff_id = staff.id')
                ->join('staff_attendance_type', 'staff_attendance.staff_attendance_type_id = staff_attendance_type.id')
                ->where('staff_attendance.is_active', 1);

        // Apply filters
        if (!empty($filters['date'])) {
            $builder->where('staff_attendance.date', $filters['date']);
        }

        if (!empty($filters['staff_id'])) {
            $builder->where('staff_attendance.staff_id', $filters['staff_id']);
        }

        if (!empty($filters['attendance_type_id'])) {
            $builder->where('staff_attendance.staff_attendance_type_id', $filters['attendance_type_id']);
        }

        if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
            $builder->where('staff_attendance.date >=', $filters['start_date']);
            $builder->where('staff_attendance.date <=', $filters['end_date']);
        }

        $builder->orderBy('staff_attendance.date', 'DESC');

        return $builder->get()->getResultArray();
    }

    /**
     * Mark attendance for staff
     */
    public function markAttendance($data)
    {
        // Check if attendance already exists for this date and staff
        $existing = $this->where('date', $data['date'])
                        ->where('staff_id', $data['staff_id'])
                        ->first();

        if ($existing) {
            // Update existing attendance
            $result = $this->update($existing['id'], $data);
            $message = 'Attendance updated successfully';
        } else {
            // Create new attendance record
            $data['is_active'] = 1;
            $result = $this->insert($data);
            $message = 'Attendance marked successfully';
        }

        if ($result) {
            return [
                'success' => true,
                'message' => $message
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to mark attendance',
            'errors' => $this->errors()
        ];
    }

    /**
     * Get attendance summary for a staff member
     */
    public function getAttendanceSummary($staffId, $month = null, $year = null)
    {
        $month = $month ?: date('m');
        $year = $year ?: date('Y');

        $builder = $this->db->table($this->table);
        $summary = $builder->select('staff_attendance_type.type, COUNT(*) as count')
                          ->join('staff_attendance_type', 'staff_attendance.staff_attendance_type_id = staff_attendance_type.id')
                          ->where('staff_id', $staffId)
                          ->where('MONTH(date)', $month)
                          ->where('YEAR(date)', $year)
                          ->where('is_active', 1)
                          ->groupBy('staff_attendance_type.id')
                          ->get()
                          ->getResultArray();

        // Calculate total working days in month
        $totalDays = cal_days_in_month(CAL_GREGORIAN, $month, $year);
        $workingDays = $this->getWorkingDaysInMonth($month, $year);

        return [
            'summary' => $summary,
            'total_days' => $totalDays,
            'working_days' => $workingDays,
            'month' => $month,
            'year' => $year
        ];
    }

    /**
     * Get monthly attendance report
     */
    public function getMonthlyReport($month = null, $year = null, $departmentId = null)
    {
        $month = $month ?: date('m');
        $year = $year ?: date('Y');

        $builder = $this->db->table('staff');
        $builder->select('staff.id, staff.name, staff.surname, staff.employee_id,
                         department.department_name,
                         COUNT(CASE WHEN staff_attendance_type.id = 1 THEN 1 END) as present_days,
                         COUNT(CASE WHEN staff_attendance_type.id = 2 THEN 1 END) as absent_days,
                         COUNT(CASE WHEN staff_attendance_type.id = 3 THEN 1 END) as late_days,
                         COUNT(CASE WHEN staff_attendance_type.id = 4 THEN 1 END) as half_days')
                ->join('department', 'staff.department = department.id', 'left')
                ->join('staff_attendance', 'staff.id = staff_attendance.staff_id AND 
                       MONTH(staff_attendance.date) = ' . $month . ' AND 
                       YEAR(staff_attendance.date) = ' . $year, 'left')
                ->join('staff_attendance_type', 'staff_attendance.staff_attendance_type_id = staff_attendance_type.id', 'left')
                ->where('staff.is_active', 1);

        if ($departmentId) {
            $builder->where('staff.department', $departmentId);
        }

        $builder->groupBy('staff.id')
                ->orderBy('staff.name', 'ASC');

        return $builder->get()->getResultArray();
    }

    /**
     * Get attendance statistics
     */
    public function getStatistics($filters = [])
    {
        $stats = [];

        // Total attendance records
        $builder = $this->where('is_active', 1);
        if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
            $builder->where('date >=', $filters['start_date'])
                   ->where('date <=', $filters['end_date']);
        }
        $stats['total_records'] = $builder->countAllResults();

        // Attendance by type
        $builder = $this->db->table($this->table);
        $builder->select('staff_attendance_type.type, COUNT(*) as count')
                ->join('staff_attendance_type', 'staff_attendance.staff_attendance_type_id = staff_attendance_type.id')
                ->where('staff_attendance.is_active', 1);

        if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
            $builder->where('staff_attendance.date >=', $filters['start_date'])
                   ->where('staff_attendance.date <=', $filters['end_date']);
        }

        $stats['by_type'] = $builder->groupBy('staff_attendance_type.id')
                                   ->get()
                                   ->getResultArray();

        // Today's attendance
        $today = date('Y-m-d');
        $stats['today_present'] = $this->where('date', $today)
                                      ->where('staff_attendance_type_id', self::ATTENDANCE_TYPE_PRESENT)
                                      ->where('is_active', 1)
                                      ->countAllResults();

        $stats['today_absent'] = $this->where('date', $today)
                                     ->where('staff_attendance_type_id', self::ATTENDANCE_TYPE_ABSENT)
                                     ->where('is_active', 1)
                                     ->countAllResults();

        return $stats;
    }

    /**
     * Sync attendance from biometric device
     */
    public function syncFromBiometric($date, $deviceData = [])
    {
        $syncedCount = 0;
        $errors = [];

        foreach ($deviceData as $record) {
            try {
                // Find staff by employee ID or biometric PIN
                $staff = $this->db->table('staff')
                                 ->where('employee_id', $record['employee_id'])
                                 ->orWhere('biometric_pin', $record['pin'])
                                 ->get()
                                 ->getRowArray();

                if (!$staff) {
                    $errors[] = "Staff not found for employee ID: {$record['employee_id']}";
                    continue;
                }

                // Determine attendance type based on time
                $attendanceType = $this->determineAttendanceType($record['scan_time']);

                $attendanceData = [
                    'date' => $date,
                    'staff_id' => $staff['id'],
                    'staff_attendance_type_id' => $attendanceType,
                    'biometric_attendence' => 1,
                    'biometric_device_data' => json_encode($record),
                    'remark' => 'Synced from biometric device',
                    'is_active' => 1
                ];

                $result = $this->markAttendance($attendanceData);
                if ($result['success']) {
                    $syncedCount++;
                } else {
                    $errors[] = "Failed to sync attendance for {$staff['name']}: " . $result['message'];
                }

            } catch (\Exception $e) {
                $errors[] = "Error processing record: " . $e->getMessage();
            }
        }

        return [
            'success' => true,
            'synced_count' => $syncedCount,
            'errors' => $errors,
            'message' => "Synced {$syncedCount} attendance records"
        ];
    }

    /**
     * Determine attendance type based on scan time
     */
    private function determineAttendanceType($scanTime)
    {
        $workStartTime = '09:00:00'; // Can be moved to config
        $lateThreshold = '09:15:00';

        if ($scanTime <= $workStartTime) {
            return self::ATTENDANCE_TYPE_PRESENT;
        } elseif ($scanTime <= $lateThreshold) {
            return self::ATTENDANCE_TYPE_PRESENT; // Within grace period
        } else {
            return self::ATTENDANCE_TYPE_LATE;
        }
    }

    /**
     * Get working days in a month (excluding weekends)
     */
    private function getWorkingDaysInMonth($month, $year)
    {
        $totalDays = cal_days_in_month(CAL_GREGORIAN, $month, $year);
        $workingDays = 0;

        for ($day = 1; $day <= $totalDays; $day++) {
            $dayOfWeek = date('N', mktime(0, 0, 0, $month, $day, $year));
            if ($dayOfWeek < 6) { // Monday to Friday
                $workingDays++;
            }
        }

        return $workingDays;
    }

    /**
     * Get staff attendance for a specific date
     */
    public function getAttendanceByDate($date)
    {
        return $this->getAttendanceWithDetails(['date' => $date]);
    }

    /**
     * Get absent staff for a date
     */
    public function getAbsentStaff($date)
    {
        // Get all active staff
        $allStaff = $this->db->table('staff')
                            ->select('id, name, surname, employee_id')
                            ->where('is_active', 1)
                            ->get()
                            ->getResultArray();

        // Get staff who have attendance marked for this date
        $presentStaff = $this->db->table($this->table)
                                ->select('staff_id')
                                ->where('date', $date)
                                ->where('is_active', 1)
                                ->get()
                                ->getResultArray();

        $presentStaffIds = array_column($presentStaff, 'staff_id');

        // Filter out present staff to get absent staff
        $absentStaff = array_filter($allStaff, function($staff) use ($presentStaffIds) {
            return !in_array($staff['id'], $presentStaffIds);
        });

        return array_values($absentStaff);
    }
}
