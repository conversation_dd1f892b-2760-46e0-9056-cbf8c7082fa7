<?php

namespace App\Models;

class StaffPayslipModel extends BaseModel
{
    protected $table = 'staff_payslip';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'staff_id', 'basic', 'total_allowance', 'total_deduction', 'leave_deduction',
        'tax', 'net_salary', 'status', 'month', 'year', 'payment_mode', 'payment_date',
        'remark', 'generated_by'
    ];

    protected $validationRules = [
        'staff_id' => 'required|integer',
        'basic' => 'required|numeric|greater_than[0]',
        'total_allowance' => 'permit_empty|numeric',
        'total_deduction' => 'permit_empty|numeric',
        'net_salary' => 'required|numeric|greater_than[0]',
        'month' => 'required|in_list[01,02,03,04,05,06,07,08,09,10,11,12]',
        'year' => 'required|integer|greater_than[2000]',
        'status' => 'permit_empty|in_list[generated,paid,cancelled]'
    ];

    protected $validationMessages = [
        'staff_id' => [
            'required' => 'Staff member is required',
            'integer' => 'Invalid staff ID'
        ],
        'basic' => [
            'required' => 'Basic salary is required',
            'numeric' => 'Basic salary must be a number',
            'greater_than' => 'Basic salary must be greater than 0'
        ],
        'net_salary' => [
            'required' => 'Net salary is required',
            'numeric' => 'Net salary must be a number',
            'greater_than' => 'Net salary must be greater than 0'
        ],
        'month' => [
            'required' => 'Month is required',
            'in_list' => 'Please select a valid month'
        ],
        'year' => [
            'required' => 'Year is required',
            'integer' => 'Year must be a number',
            'greater_than' => 'Please enter a valid year'
        ]
    ];

    protected $searchableColumns = ['month', 'year', 'status'];
    protected $orderableColumns = ['id', 'month', 'year', 'net_salary', 'status', 'created_at'];

    /**
     * Get payslips with staff details
     */
    public function getPayslipsWithDetails($filters = [])
    {
        $builder = $this->db->table($this->table);
        $builder->select('staff_payslip.*, staff.name, staff.surname, staff.employee_id,
                         department.department_name, staff_designation.designation')
                ->join('staff', 'staff_payslip.staff_id = staff.id')
                ->join('department', 'staff.department = department.id', 'left')
                ->join('staff_designation', 'staff.designation = staff_designation.id', 'left');

        // Apply filters
        if (!empty($filters['staff_id'])) {
            $builder->where('staff_payslip.staff_id', $filters['staff_id']);
        }

        if (!empty($filters['month'])) {
            $builder->where('staff_payslip.month', $filters['month']);
        }

        if (!empty($filters['year'])) {
            $builder->where('staff_payslip.year', $filters['year']);
        }

        if (!empty($filters['status'])) {
            $builder->where('staff_payslip.status', $filters['status']);
        }

        if (!empty($filters['department_id'])) {
            $builder->where('staff.department', $filters['department_id']);
        }

        $builder->orderBy('staff_payslip.year', 'DESC')
                ->orderBy('staff_payslip.month', 'DESC')
                ->orderBy('staff.name', 'ASC');

        return $builder->get()->getResultArray();
    }

    /**
     * Generate payslip for staff
     */
    public function generatePayslip($staffId, $month, $year, $payrollData = [])
    {
        // Check if payslip already exists
        $existing = $this->where('staff_id', $staffId)
                        ->where('month', $month)
                        ->where('year', $year)
                        ->first();

        if ($existing) {
            return [
                'success' => false,
                'message' => 'Payslip already exists for this month and year'
            ];
        }

        // Get staff details
        $staffModel = new StaffModel();
        $staff = $staffModel->find($staffId);

        if (!$staff) {
            return [
                'success' => false,
                'message' => 'Staff member not found'
            ];
        }

        // Get payroll details
        $payrollModel = new StaffPayrollModel();
        $payroll = $payrollModel->find($staff['payscale']);

        if (!$payroll) {
            return [
                'success' => false,
                'message' => 'Payroll scale not found for staff member'
            ];
        }

        // Calculate salary components
        $basicSalary = $payroll['basic_salary'];
        $allowances = $payrollData['allowances'] ?? [];
        $deductions = $payrollData['deductions'] ?? [];
        
        $totalAllowances = array_sum($allowances);
        $totalDeductions = array_sum($deductions);
        
        // Calculate leave deduction
        $leaveDeduction = $this->calculateLeaveDeduction($staffId, $month, $year, $basicSalary);
        
        // Calculate tax (simplified - can be enhanced)
        $grossSalary = $basicSalary + $totalAllowances;
        $tax = $this->calculateTax($grossSalary);
        
        $netSalary = $grossSalary - $totalDeductions - $leaveDeduction - $tax;

        $payslipData = [
            'staff_id' => $staffId,
            'basic' => $basicSalary,
            'total_allowance' => $totalAllowances,
            'total_deduction' => $totalDeductions,
            'leave_deduction' => $leaveDeduction,
            'tax' => $tax,
            'net_salary' => $netSalary,
            'status' => 'generated',
            'month' => str_pad($month, 2, '0', STR_PAD_LEFT),
            'year' => $year,
            'generated_by' => user_id(),
            'remark' => $payrollData['remark'] ?? ''
        ];

        if ($this->insert($payslipData)) {
            return [
                'success' => true,
                'message' => 'Payslip generated successfully',
                'data' => $this->find($this->getInsertID())
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to generate payslip',
            'errors' => $this->errors()
        ];
    }

    /**
     * Mark payslip as paid
     */
    public function markAsPaid($id, $paymentMode = 'bank_transfer', $paymentDate = null)
    {
        $paymentDate = $paymentDate ?: date('Y-m-d');

        $data = [
            'status' => 'paid',
            'payment_mode' => $paymentMode,
            'payment_date' => $paymentDate
        ];

        if ($this->update($id, $data)) {
            return [
                'success' => true,
                'message' => 'Payslip marked as paid successfully'
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to update payslip status'
        ];
    }

    /**
     * Cancel payslip
     */
    public function cancelPayslip($id, $reason = '')
    {
        $data = [
            'status' => 'cancelled',
            'remark' => $reason
        ];

        if ($this->update($id, $data)) {
            return [
                'success' => true,
                'message' => 'Payslip cancelled successfully'
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to cancel payslip'
        ];
    }

    /**
     * Get payslip statistics
     */
    public function getStatistics($filters = [])
    {
        $stats = [];

        // Total payslips
        $builder = $this->db->table($this->table);
        if (!empty($filters['month'])) {
            $builder->where('month', $filters['month']);
        }
        if (!empty($filters['year'])) {
            $builder->where('year', $filters['year']);
        }
        $stats['total_payslips'] = $builder->countAllResults();

        // Payslips by status
        $statusStats = $this->select('status, COUNT(*) as count')
                           ->groupBy('status');
        
        if (!empty($filters['month'])) {
            $statusStats->where('month', $filters['month']);
        }
        if (!empty($filters['year'])) {
            $statusStats->where('year', $filters['year']);
        }
        
        $stats['by_status'] = $statusStats->findAll();

        // Total salary disbursed
        $salaryStats = $this->selectSum('net_salary', 'total_disbursed')
                           ->where('status', 'paid');
        
        if (!empty($filters['month'])) {
            $salaryStats->where('month', $filters['month']);
        }
        if (!empty($filters['year'])) {
            $salaryStats->where('year', $filters['year']);
        }
        
        $salaryResult = $salaryStats->first();
        $stats['total_disbursed'] = $salaryResult['total_disbursed'] ?? 0;

        // Average salary
        $avgStats = $this->selectAvg('net_salary', 'avg_salary');
        
        if (!empty($filters['month'])) {
            $avgStats->where('month', $filters['month']);
        }
        if (!empty($filters['year'])) {
            $avgStats->where('year', $filters['year']);
        }
        
        $avgResult = $avgStats->first();
        $stats['average_salary'] = round($avgResult['avg_salary'] ?? 0, 2);

        return $stats;
    }

    /**
     * Get monthly payroll summary
     */
    public function getMonthlyPayrollSummary($month, $year)
    {
        $builder = $this->db->table($this->table);
        $summary = $builder->select('staff_payslip.*, staff.name, staff.surname, staff.employee_id,
                                   department.department_name')
                          ->join('staff', 'staff_payslip.staff_id = staff.id')
                          ->join('department', 'staff.department = department.id', 'left')
                          ->where('staff_payslip.month', str_pad($month, 2, '0', STR_PAD_LEFT))
                          ->where('staff_payslip.year', $year)
                          ->orderBy('department.department_name', 'ASC')
                          ->orderBy('staff.name', 'ASC')
                          ->get()
                          ->getResultArray();

        // Calculate totals
        $totals = [
            'total_basic' => 0,
            'total_allowances' => 0,
            'total_deductions' => 0,
            'total_tax' => 0,
            'total_net' => 0,
            'staff_count' => count($summary)
        ];

        foreach ($summary as $payslip) {
            $totals['total_basic'] += $payslip['basic'];
            $totals['total_allowances'] += $payslip['total_allowance'];
            $totals['total_deductions'] += $payslip['total_deduction'];
            $totals['total_tax'] += $payslip['tax'];
            $totals['total_net'] += $payslip['net_salary'];
        }

        return [
            'payslips' => $summary,
            'totals' => $totals,
            'month' => $month,
            'year' => $year
        ];
    }

    /**
     * Bulk generate payslips for all staff
     */
    public function bulkGeneratePayslips($month, $year, $departmentId = null)
    {
        $staffModel = new StaffModel();
        
        $builder = $staffModel->where('is_active', 1);
        if ($departmentId) {
            $builder->where('department', $departmentId);
        }
        
        $staffList = $builder->findAll();
        
        $generated = 0;
        $errors = [];

        foreach ($staffList as $staff) {
            $result = $this->generatePayslip($staff['id'], $month, $year);
            
            if ($result['success']) {
                $generated++;
            } else {
                $errors[] = "Failed to generate payslip for {$staff['name']} {$staff['surname']}: {$result['message']}";
            }
        }

        return [
            'success' => true,
            'generated_count' => $generated,
            'total_staff' => count($staffList),
            'errors' => $errors,
            'message' => "Generated {$generated} payslips out of " . count($staffList) . " staff members"
        ];
    }

    /**
     * Calculate leave deduction based on attendance
     */
    private function calculateLeaveDeduction($staffId, $month, $year, $basicSalary)
    {
        $attendanceModel = new StaffAttendanceModel();
        
        // Get absent days for the month
        $absentDays = $attendanceModel->where('staff_id', $staffId)
                                    ->where('MONTH(date)', $month)
                                    ->where('YEAR(date)', $year)
                                    ->where('staff_attendance_type_id', 2) // Absent
                                    ->countAllResults();

        // Calculate per day salary
        $workingDays = $this->getWorkingDaysInMonth($month, $year);
        $perDaySalary = $basicSalary / $workingDays;

        return $absentDays * $perDaySalary;
    }

    /**
     * Calculate tax (simplified calculation)
     */
    private function calculateTax($grossSalary)
    {
        // Simplified tax calculation - can be enhanced based on tax slabs
        if ($grossSalary <= 20000) {
            return 0;
        } elseif ($grossSalary <= 50000) {
            return $grossSalary * 0.05; // 5%
        } else {
            return $grossSalary * 0.10; // 10%
        }
    }

    /**
     * Get working days in a month
     */
    private function getWorkingDaysInMonth($month, $year)
    {
        $totalDays = cal_days_in_month(CAL_GREGORIAN, $month, $year);
        $workingDays = 0;

        for ($day = 1; $day <= $totalDays; $day++) {
            $dayOfWeek = date('N', mktime(0, 0, 0, $month, $day, $year));
            if ($dayOfWeek < 6) { // Monday to Friday
                $workingDays++;
            }
        }

        return $workingDays;
    }

    /**
     * Get payslip details with breakdown
     */
    public function getPayslipDetails($id)
    {
        $payslip = $this->getPayslipsWithDetails(['id' => $id]);
        
        if (empty($payslip)) {
            return null;
        }

        $payslip = $payslip[0];
        
        // Add calculated fields
        $payslip['gross_salary'] = $payslip['basic'] + $payslip['total_allowance'];
        $payslip['total_deductions_all'] = $payslip['total_deduction'] + $payslip['leave_deduction'] + $payslip['tax'];

        return $payslip;
    }
}
