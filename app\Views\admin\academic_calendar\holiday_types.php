<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<!-- Breadcrumb -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <h2 class="text-title-md2 font-semibold text-black dark:text-white">
        Holiday Types Management
    </h2>

    <nav>
        <ol class="flex items-center gap-2">
            <?php if (!empty($breadcrumbs)): ?>
                <?php foreach ($breadcrumbs as $breadcrumb): ?>
                    <li>
                        <?php if (!empty($breadcrumb['url'])): ?>
                            <a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['name'] ?> /</a>
                        <?php else: ?>
                            <span class="font-medium text-primary"><?= $breadcrumb['name'] ?></span>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            <?php endif; ?>
        </ol>
    </nav>
</div>

<!-- Actions -->
<div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark mb-6">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <div class="flex items-center justify-between">
            <h3 class="font-medium text-black dark:text-white">Holiday Types</h3>
            <div class="flex gap-2">
                <button onclick="addHolidayType()" class="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-center font-medium text-white hover:bg-opacity-90">
                    <i class="fas fa-plus mr-2"></i>
                    Add Holiday Type
                </button>
                <a href="<?= base_url($route_prefix) ?>" class="inline-flex items-center justify-center rounded-md border border-stroke px-4 py-2 text-center font-medium text-black hover:bg-gray dark:border-strokedark dark:text-white">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Calendar
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Data Table -->
<div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <h3 class="font-medium text-black dark:text-white">Holiday Types List</h3>
    </div>
    
    <div class="p-7">
        <div class="overflow-x-auto">
            <table id="holiday-types-table" class="w-full table-auto">
                <thead>
                    <tr class="bg-gray-2 text-left dark:bg-meta-4">
                        <th class="min-w-[50px] py-4 px-4 font-medium text-black dark:text-white">#</th>
                        <th class="min-w-[150px] py-4 px-4 font-medium text-black dark:text-white">Name</th>
                        <th class="min-w-[200px] py-4 px-4 font-medium text-black dark:text-white">Description</th>
                        <th class="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">Color</th>
                        <th class="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">Academic Break</th>
                        <th class="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">Affects Attendance</th>
                        <th class="min-w-[80px] py-4 px-4 font-medium text-black dark:text-white">Usage</th>
                        <th class="min-w-[80px] py-4 px-4 font-medium text-black dark:text-white">Status</th>
                        <th class="py-4 px-4 font-medium text-black dark:text-white">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded via DataTables AJAX -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add/Edit Holiday Type Modal -->
<div id="holiday-type-modal" class="fixed inset-0 z-50 hidden items-center justify-center bg-black bg-opacity-50">
    <div class="w-full max-w-md rounded-lg bg-white p-6 dark:bg-boxdark">
        <div class="mb-4 flex items-center justify-between">
            <h3 id="type-modal-title" class="text-lg font-semibold text-black dark:text-white">Add Holiday Type</h3>
            <button onclick="closeHolidayTypeModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <form id="holiday-type-form">
            <input type="hidden" id="type-id" name="id">
            
            <div class="mb-4">
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">Name <span class="text-red-500">*</span></label>
                <input type="text" id="type-name" name="name" class="w-full rounded border border-stroke bg-gray py-3 px-4 text-black focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:text-white" required>
            </div>
            
            <div class="mb-4">
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">Description</label>
                <textarea id="type-description" name="description" rows="3" class="w-full rounded border border-stroke bg-gray py-3 px-4 text-black focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:text-white"></textarea>
            </div>
            
            <div class="mb-4">
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">Color <span class="text-red-500">*</span></label>
                <div class="flex gap-2">
                    <input type="color" id="type-color" name="color" class="w-16 h-10 rounded border border-stroke" value="#007bff" required>
                    <input type="text" id="type-color-text" class="flex-1 rounded border border-stroke bg-gray py-3 px-4 text-black focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:text-white" placeholder="#007bff" required>
                </div>
            </div>
            
            <div class="mb-4">
                <label class="flex items-center">
                    <input type="checkbox" id="type-academic-break" name="is_academic_break" value="1" class="mr-2">
                    <span class="text-sm font-medium text-black dark:text-white">Is Academic Break</span>
                </label>
                <p class="text-xs text-gray-500 mt-1">Check if this holiday type represents an academic break period</p>
            </div>
            
            <div class="mb-4">
                <label class="flex items-center">
                    <input type="checkbox" id="type-affects-attendance" name="affects_attendance" value="1" class="mr-2" checked>
                    <span class="text-sm font-medium text-black dark:text-white">Affects Attendance</span>
                </label>
                <p class="text-xs text-gray-500 mt-1">Check if attendance cannot be marked on this type of holiday</p>
            </div>
            
            <div class="mb-4">
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">Status</label>
                <select id="type-status" name="is_active" class="w-full rounded border border-stroke bg-gray py-3 px-4 text-black focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:text-white">
                    <option value="yes">Active</option>
                    <option value="no">Inactive</option>
                </select>
            </div>
            
            <div class="flex gap-4">
                <button type="submit" class="flex-1 rounded bg-primary py-2 px-4 text-white hover:bg-opacity-90">
                    <i class="fas fa-save mr-2"></i>
                    Save Type
                </button>
                <button type="button" onclick="closeHolidayTypeModal()" class="flex-1 rounded border border-stroke py-2 px-4 text-black hover:bg-gray dark:border-strokedark dark:text-white">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    initializeDataTable();
    
    // Holiday type form submission
    $('#holiday-type-form').on('submit', function(e) {
        e.preventDefault();
        saveHolidayType();
    });
    
    // Color picker sync
    $('#type-color').on('change', function() {
        $('#type-color-text').val($(this).val());
    });
    
    $('#type-color-text').on('input', function() {
        const color = $(this).val();
        if (/^#[0-9A-F]{6}$/i.test(color)) {
            $('#type-color').val(color);
        }
    });
});

let table;

function initializeDataTable() {
    table = $('#holiday-types-table').DataTable({
        processing: true,
        serverSide: false,
        ajax: {
            url: '<?= base_url($route_prefix . '/holiday-types-data') ?>',
            type: 'GET'
        },
        columns: [
            { data: 'id', name: 'id' },
            { data: 'name', name: 'name' },
            { data: 'description', name: 'description' },
            { data: 'color', name: 'color', orderable: false },
            { data: 'is_academic_break', name: 'is_academic_break', orderable: false },
            { data: 'affects_attendance', name: 'affects_attendance', orderable: false },
            { data: 'usage_count', name: 'usage_count' },
            { data: 'is_active', name: 'is_active', orderable: false },
            { data: 'actions', name: 'actions', orderable: false, searchable: false }
        ],
        order: [[1, 'asc']],
        pageLength: 25,
        responsive: true,
        language: {
            processing: "Loading holiday types...",
            emptyTable: "No holiday types found"
        }
    });
}

function addHolidayType() {
    $('#type-modal-title').text('Add Holiday Type');
    $('#holiday-type-form')[0].reset();
    $('#type-id').val('');
    $('#type-color').val('#007bff');
    $('#type-color-text').val('#007bff');
    $('#type-affects-attendance').prop('checked', true);
    $('#holiday-type-modal').removeClass('hidden').addClass('flex');
}

function editHolidayType(id) {
    // This would be implemented to load and edit holiday type data
    // For now, just show the modal
    $('#type-modal-title').text('Edit Holiday Type');
    $('#type-id').val(id);
    $('#holiday-type-modal').removeClass('hidden').addClass('flex');
    
    // TODO: Load holiday type data and populate form
}

function deleteHolidayType(id) {
    Swal.fire({
        title: 'Delete Holiday Type',
        text: 'Are you sure you want to delete this holiday type? This action cannot be undone.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        confirmButtonText: 'Delete'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '<?= base_url($route_prefix . '/holiday-types/delete') ?>/' + id,
                type: 'DELETE',
                data: {
                    [csrf_token]: csrf_hash
                }
            })
            .done(function(response) {
                if (response.success) {
                    showToast(response.message, 'success');
                    table.ajax.reload();
                } else {
                    showToast(response.message, 'error');
                }
            })
            .fail(function() {
                showToast('Failed to delete holiday type', 'error');
            });
        }
    });
}

function closeHolidayTypeModal() {
    $('#holiday-type-modal').removeClass('flex').addClass('hidden');
}

function saveHolidayType() {
    const formData = new FormData($('#holiday-type-form')[0]);
    formData.append(csrf_token, csrf_hash);
    
    const typeId = $('#type-id').val();
    const url = typeId ? 
        '<?= base_url($route_prefix . '/holiday-types/update') ?>/' + typeId : 
        '<?= base_url($route_prefix . '/holiday-types/store') ?>';
    
    $.ajax({
        url: url,
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false
    })
    .done(function(response) {
        if (response.success) {
            showToast(response.message, 'success');
            closeHolidayTypeModal();
            table.ajax.reload();
        } else {
            showToast(response.message, 'error');
        }
    })
    .fail(function() {
        showToast('Failed to save holiday type', 'error');
    });
}

// Make functions global
window.editHolidayType = editHolidayType;
window.deleteHolidayType = deleteHolidayType;
</script>
<?= $this->endSection() ?>
