<?php

namespace App\Controllers;

use App\Models\StaffPayrollModel;
use App\Models\StaffPayslipModel;
use App\Models\StaffModel;
use App\Models\DepartmentModel;

class StaffPayrollController extends BaseCrudController
{
    protected $staffPayslipModel;
    protected $staffModel;
    protected $departmentModel;

    public function __construct()
    {
        parent::__construct();
        $this->model = new StaffPayrollModel();
        $this->staffPayslipModel = new StaffPayslipModel();
        $this->staffModel = new StaffModel();
        $this->departmentModel = new DepartmentModel();
        
        $this->viewPath = 'admin/staff_payroll';
        $this->routePrefix = 'admin/staff-payroll';
        $this->entityName = 'Staff Payroll';
        $this->entityNamePlural = 'Staff Payroll';
    }

    /**
     * Display payroll scales list
     */
    public function index()
    {
        $data = [
            'title' => 'Staff Payroll Scales',
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => 'Staff Payroll', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/index', $data);
    }

    /**
     * Get payroll scales data for DataTables
     */
    public function getData()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $payrollScales = $this->model->getWithUsageCount();

        $data = [];
        foreach ($payrollScales as $scale) {
            $data[] = [
                'id' => $scale['id'],
                'pay_scale' => $scale['pay_scale'],
                'grade' => $scale['grade'],
                'basic_salary' => '₹' . number_format($scale['basic_salary'], 2),
                'usage_count' => $scale['usage_count'],
                'is_active' => $scale['is_active'] === 'yes' ? 
                    '<span class="badge badge-success">Active</span>' : 
                    '<span class="badge badge-secondary">Inactive</span>',
                'actions' => $this->generateActionButtons($scale)
            ];
        }

        return $this->response->setJSON(['data' => $data]);
    }

    /**
     * Show create form
     */
    public function create()
    {
        $data = [
            'title' => 'Create Payroll Scale',
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => base_url($this->routePrefix)],
                ['name' => 'Create', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/create', $data);
    }

    /**
     * Store payroll scale
     */
    public function store()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $data = $this->request->getPost();
        $result = $this->model->createPayrollScale($data);
        
        return $this->response->setJSON($result);
    }

    /**
     * Show edit form
     */
    public function edit($id)
    {
        $payrollScale = $this->model->find($id);
        
        if (!$payrollScale) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Payroll scale not found');
        }

        $data = [
            'title' => 'Edit Payroll Scale',
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'payroll_scale' => $payrollScale,
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => base_url($this->routePrefix)],
                ['name' => 'Edit', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/edit', $data);
    }

    /**
     * Update payroll scale
     */
    public function update($id)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $data = $this->request->getPost();
        $result = $this->model->updatePayrollScale($id, $data);
        
        return $this->response->setJSON($result);
    }

    /**
     * Delete payroll scale
     */
    public function delete($id)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $result = $this->model->deletePayrollScale($id);
        return $this->response->setJSON($result);
    }

    /**
     * Show payslips list
     */
    public function payslips()
    {
        $data = [
            'title' => 'Staff Payslips',
            'entity_name' => 'Staff Payslip',
            'route_prefix' => $this->routePrefix,
            'staff_list' => $this->staffModel->getForDropdown(),
            'departments' => $this->departmentModel->getForDropdown(),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => 'Staff Payroll', 'url' => base_url($this->routePrefix)],
                ['name' => 'Payslips', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/payslips', $data);
    }

    /**
     * Get payslips data for DataTables
     */
    public function getPayslipsData()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $filters = [
            'staff_id' => $this->request->getGet('staff_id'),
            'month' => $this->request->getGet('month'),
            'year' => $this->request->getGet('year'),
            'status' => $this->request->getGet('status'),
            'department_id' => $this->request->getGet('department_id')
        ];

        $payslips = $this->staffPayslipModel->getPayslipsWithDetails($filters);

        $data = [];
        foreach ($payslips as $payslip) {
            $statusBadge = $this->getPayslipStatusBadge($payslip['status']);
            
            $data[] = [
                'id' => $payslip['id'],
                'staff_name' => $payslip['name'] . ' ' . $payslip['surname'],
                'employee_id' => $payslip['employee_id'],
                'department' => $payslip['department_name'] ?? 'Not assigned',
                'month_year' => date('M Y', mktime(0, 0, 0, $payslip['month'], 1, $payslip['year'])),
                'basic_salary' => '₹' . number_format($payslip['basic'], 2),
                'net_salary' => '₹' . number_format($payslip['net_salary'], 2),
                'status' => $statusBadge,
                'actions' => $this->generatePayslipActionButtons($payslip)
            ];
        }

        return $this->response->setJSON(['data' => $data]);
    }

    /**
     * Show generate payslip form
     */
    public function generatePayslip()
    {
        $data = [
            'title' => 'Generate Payslip',
            'entity_name' => 'Staff Payslip',
            'route_prefix' => $this->routePrefix,
            'staff_list' => $this->staffModel->getForDropdown(),
            'departments' => $this->departmentModel->getForDropdown(),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => 'Staff Payroll', 'url' => base_url($this->routePrefix)],
                ['name' => 'Generate Payslip', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/generate_payslip', $data);
    }

    /**
     * Store generated payslip
     */
    public function storePayslip()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $staffId = $this->request->getPost('staff_id');
        $month = $this->request->getPost('month');
        $year = $this->request->getPost('year');
        $payrollData = $this->request->getPost();

        $result = $this->staffPayslipModel->generatePayslip($staffId, $month, $year, $payrollData);
        
        return $this->response->setJSON($result);
    }

    /**
     * Bulk generate payslips
     */
    public function bulkGeneratePayslips()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $month = $this->request->getPost('month');
        $year = $this->request->getPost('year');
        $departmentId = $this->request->getPost('department_id');

        $result = $this->staffPayslipModel->bulkGeneratePayslips($month, $year, $departmentId);
        
        return $this->response->setJSON($result);
    }

    /**
     * Show payslip details
     */
    public function showPayslip($id)
    {
        $payslip = $this->staffPayslipModel->getPayslipDetails($id);
        
        if (!$payslip) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Payslip not found');
        }

        $data = [
            'title' => 'Payslip Details',
            'entity_name' => 'Staff Payslip',
            'route_prefix' => $this->routePrefix,
            'payslip' => $payslip,
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => 'Staff Payroll', 'url' => base_url($this->routePrefix)],
                ['name' => 'Payslips', 'url' => base_url($this->routePrefix . '/payslips')],
                ['name' => 'Details', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/show_payslip', $data);
    }

    /**
     * Mark payslip as paid
     */
    public function markAsPaid($id)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $paymentMode = $this->request->getPost('payment_mode') ?: 'bank_transfer';
        $paymentDate = $this->request->getPost('payment_date') ?: date('Y-m-d');

        $result = $this->staffPayslipModel->markAsPaid($id, $paymentMode, $paymentDate);
        
        return $this->response->setJSON($result);
    }

    /**
     * Cancel payslip
     */
    public function cancelPayslip($id)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $reason = $this->request->getPost('reason');
        $result = $this->staffPayslipModel->cancelPayslip($id, $reason);
        
        return $this->response->setJSON($result);
    }

    /**
     * Show payroll report
     */
    public function report()
    {
        $month = $this->request->getGet('month') ?: date('m');
        $year = $this->request->getGet('year') ?: date('Y');

        $data = [
            'title' => 'Payroll Report',
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'month' => $month,
            'year' => $year,
            'report_data' => $this->staffPayslipModel->getMonthlyPayrollSummary($month, $year),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => 'Staff Payroll', 'url' => base_url($this->routePrefix)],
                ['name' => 'Report', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/report', $data);
    }

    /**
     * Get payroll statistics
     */
    public function getStatistics()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $filters = [
            'month' => $this->request->getGet('month'),
            'year' => $this->request->getGet('year')
        ];

        $payrollStats = $this->model->getStatistics();
        $payslipStats = $this->staffPayslipModel->getStatistics($filters);

        return $this->response->setJSON([
            'payroll_scales' => $payrollStats,
            'payslips' => $payslipStats
        ]);
    }

    /**
     * Create default payroll scales
     */
    public function createDefaults()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $result = $this->model->createDefaultPayrollScales();
        return $this->response->setJSON($result);
    }

    /**
     * Generate payslip status badge
     */
    private function getPayslipStatusBadge($status)
    {
        switch ($status) {
            case 'generated':
                return '<span class="badge badge-warning">Generated</span>';
            case 'paid':
                return '<span class="badge badge-success">Paid</span>';
            case 'cancelled':
                return '<span class="badge badge-danger">Cancelled</span>';
            default:
                return '<span class="badge badge-secondary">' . ucfirst($status) . '</span>';
        }
    }

    /**
     * Generate action buttons for payroll scales
     */
    private function generateActionButtons($scale)
    {
        $buttons = [];
        
        // Edit button
        $buttons[] = '<a href="' . base_url($this->routePrefix . '/edit/' . $scale['id']) . '" 
                        class="btn btn-sm btn-warning" title="Edit">
                        <i class="fas fa-edit"></i>
                      </a>';
        
        // Delete button (only if not in use)
        if ($scale['usage_count'] == 0) {
            $buttons[] = '<button onclick="deletePayrollScale(' . $scale['id'] . ')" 
                            class="btn btn-sm btn-danger" title="Delete">
                            <i class="fas fa-trash"></i>
                          </button>';
        } else {
            $buttons[] = '<button class="btn btn-sm btn-secondary" disabled title="Cannot delete - in use">
                            <i class="fas fa-trash"></i>
                          </button>';
        }
        
        return implode(' ', $buttons);
    }

    /**
     * Generate action buttons for payslips
     */
    private function generatePayslipActionButtons($payslip)
    {
        $buttons = [];
        
        // View button
        $buttons[] = '<a href="' . base_url($this->routePrefix . '/payslip/' . $payslip['id']) . '" 
                        class="btn btn-sm btn-info" title="View">
                        <i class="fas fa-eye"></i>
                      </a>';
        
        // Mark as paid button (only for generated payslips)
        if ($payslip['status'] === 'generated') {
            $buttons[] = '<button onclick="markAsPaid(' . $payslip['id'] . ')" 
                            class="btn btn-sm btn-success" title="Mark as Paid">
                            <i class="fas fa-check"></i>
                          </button>';
        }
        
        // Cancel button (only for generated payslips)
        if ($payslip['status'] === 'generated') {
            $buttons[] = '<button onclick="cancelPayslip(' . $payslip['id'] . ')" 
                            class="btn btn-sm btn-danger" title="Cancel">
                            <i class="fas fa-ban"></i>
                          </button>';
        }
        
        return implode(' ', $buttons);
    }
}
