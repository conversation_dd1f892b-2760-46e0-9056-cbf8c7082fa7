<?php

namespace App\Controllers;

use App\Models\StaffAttendanceModel;
use App\Models\StaffAttendanceTypeModel;
use App\Models\StaffModel;
use App\Models\DepartmentModel;

class StaffAttendanceController extends BaseCrudController
{
    protected $staffAttendanceTypeModel;
    protected $staffModel;
    protected $departmentModel;

    public function __construct()
    {
        parent::__construct();
        $this->model = new StaffAttendanceModel();
        $this->staffAttendanceTypeModel = new StaffAttendanceTypeModel();
        $this->staffModel = new StaffModel();
        $this->departmentModel = new DepartmentModel();
        
        $this->viewPath = 'admin/staff_attendance';
        $this->routePrefix = 'admin/staff-attendance';
        $this->entityName = 'Staff Attendance';
        $this->entityNamePlural = 'Staff Attendance';
    }

    /**
     * Display attendance list
     */
    public function index()
    {
        $data = [
            'title' => 'Staff Attendance',
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'staff_list' => $this->staffModel->getForDropdown(),
            'attendance_types' => $this->staffAttendanceTypeModel->getForDropdown(),
            'departments' => $this->departmentModel->getForDropdown(),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => 'Staff Attendance', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/index', $data);
    }

    /**
     * Get attendance data for DataTables
     */
    public function getData()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $filters = [
            'date' => $this->request->getGet('date'),
            'staff_id' => $this->request->getGet('staff_id'),
            'attendance_type_id' => $this->request->getGet('attendance_type_id'),
            'start_date' => $this->request->getGet('start_date'),
            'end_date' => $this->request->getGet('end_date')
        ];

        $attendance = $this->model->getAttendanceWithDetails($filters);

        $data = [];
        foreach ($attendance as $record) {
            $data[] = [
                'id' => $record['id'],
                'date' => date('M d, Y', strtotime($record['date'])),
                'staff_name' => $record['name'] . ' ' . $record['surname'],
                'employee_id' => $record['employee_id'],
                'attendance_type' => $record['attendance_type'],
                'remark' => $record['remark'] ?: 'No remarks',
                'biometric' => $record['biometric_attendence'] ? 'Yes' : 'No',
                'qr_code' => $record['qrcode_attendance'] ? 'Yes' : 'No',
                'actions' => $this->generateActionButtons($record)
            ];
        }

        return $this->response->setJSON(['data' => $data]);
    }

    /**
     * Show mark attendance form
     */
    public function markAttendance()
    {
        $date = $this->request->getGet('date') ?: date('Y-m-d');
        
        $data = [
            'title' => 'Mark Staff Attendance',
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'date' => $date,
            'staff_list' => $this->staffModel->getStaffWithDetails(),
            'attendance_types' => $this->staffAttendanceTypeModel->getActive(),
            'existing_attendance' => $this->model->getAttendanceByDate($date),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => 'Staff Attendance', 'url' => base_url($this->routePrefix)],
                ['name' => 'Mark Attendance', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/mark_attendance', $data);
    }

    /**
     * Store attendance
     */
    public function store()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $data = $this->request->getPost();
        $result = $this->model->markAttendance($data);
        
        return $this->response->setJSON($result);
    }

    /**
     * Bulk mark attendance
     */
    public function bulkMarkAttendance()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $attendanceData = $this->request->getPost('attendance');
        $date = $this->request->getPost('date');

        if (empty($attendanceData) || empty($date)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid attendance data'
            ]);
        }

        $successCount = 0;
        $errors = [];

        foreach ($attendanceData as $staffId => $attendanceInfo) {
            if (empty($attendanceInfo['attendance_type_id'])) {
                continue; // Skip if no attendance type selected
            }

            $data = [
                'date' => $date,
                'staff_id' => $staffId,
                'staff_attendance_type_id' => $attendanceInfo['attendance_type_id'],
                'remark' => $attendanceInfo['remark'] ?? ''
            ];

            $result = $this->model->markAttendance($data);
            
            if ($result['success']) {
                $successCount++;
            } else {
                $errors[] = "Failed to mark attendance for staff ID {$staffId}: " . $result['message'];
            }
        }

        return $this->response->setJSON([
            'success' => true,
            'message' => "Successfully marked attendance for {$successCount} staff members",
            'success_count' => $successCount,
            'errors' => $errors
        ]);
    }

    /**
     * Show attendance report
     */
    public function report()
    {
        $month = $this->request->getGet('month') ?: date('m');
        $year = $this->request->getGet('year') ?: date('Y');
        $departmentId = $this->request->getGet('department_id');

        $data = [
            'title' => 'Staff Attendance Report',
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'month' => $month,
            'year' => $year,
            'department_id' => $departmentId,
            'departments' => $this->departmentModel->getForDropdown(),
            'report_data' => $this->model->getMonthlyReport($month, $year, $departmentId),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => 'Staff Attendance', 'url' => base_url($this->routePrefix)],
                ['name' => 'Report', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/report', $data);
    }

    /**
     * Get attendance statistics
     */
    public function getStatistics()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $filters = [
            'start_date' => $this->request->getGet('start_date'),
            'end_date' => $this->request->getGet('end_date')
        ];

        $stats = $this->model->getStatistics($filters);
        return $this->response->setJSON($stats);
    }

    /**
     * Sync attendance from biometric device
     */
    public function syncFromBiometric()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $date = $this->request->getPost('date');
        $deviceData = $this->request->getPost('device_data');

        if (empty($date) || empty($deviceData)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Date and device data are required'
            ]);
        }

        $result = $this->model->syncFromBiometric($date, $deviceData);
        return $this->response->setJSON($result);
    }

    /**
     * Show edit form
     */
    public function edit($id)
    {
        $attendance = $this->model->getAttendanceWithDetails(['id' => $id]);
        
        if (empty($attendance)) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Attendance record not found');
        }

        $attendance = $attendance[0];

        $data = [
            'title' => 'Edit Attendance',
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'attendance' => $attendance,
            'attendance_types' => $this->staffAttendanceTypeModel->getForDropdown(),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => 'Staff Attendance', 'url' => base_url($this->routePrefix)],
                ['name' => 'Edit', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/edit', $data);
    }

    /**
     * Update attendance
     */
    public function update($id)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $data = $this->request->getPost();
        
        if ($this->model->update($id, $data)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Attendance updated successfully'
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to update attendance',
            'errors' => $this->model->errors()
        ]);
    }

    /**
     * Delete attendance
     */
    public function delete($id)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        if ($this->model->delete($id)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Attendance record deleted successfully'
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to delete attendance record'
        ]);
    }

    /**
     * Get absent staff for a date
     */
    public function getAbsentStaff()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $date = $this->request->getGet('date') ?: date('Y-m-d');
        $absentStaff = $this->model->getAbsentStaff($date);

        return $this->response->setJSON([
            'success' => true,
            'data' => $absentStaff,
            'date' => $date
        ]);
    }

    /**
     * Generate action buttons for DataTable
     */
    private function generateActionButtons($attendance)
    {
        $buttons = [];
        
        // Edit button
        $buttons[] = '<a href="' . base_url($this->routePrefix . '/edit/' . $attendance['id']) . '" 
                        class="btn btn-sm btn-warning" title="Edit">
                        <i class="fas fa-edit"></i>
                      </a>';
        
        // Delete button
        $buttons[] = '<button onclick="deleteAttendance(' . $attendance['id'] . ')" 
                        class="btn btn-sm btn-danger" title="Delete">
                        <i class="fas fa-trash"></i>
                      </button>';
        
        return implode(' ', $buttons);
    }
}
