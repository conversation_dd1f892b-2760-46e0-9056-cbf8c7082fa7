<?php

namespace App\Models;

class StaffAttendanceTypeModel extends BaseModel
{
    protected $table = 'staff_attendance_type';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'type', 'key_value', 'is_active', 'for_qr_attendance', 
        'long_lang_name', 'long_name_style'
    ];

    protected $validationRules = [
        'type' => 'required|min_length[2]|max_length[200]|is_unique[staff_attendance_type.type,id,{id}]',
        'key_value' => 'required|min_length[1]|max_length[200]|is_unique[staff_attendance_type.key_value,id,{id}]'
    ];

    protected $validationMessages = [
        'type' => [
            'required' => 'Attendance type is required',
            'min_length' => 'Attendance type must be at least 2 characters long',
            'max_length' => 'Attendance type cannot exceed 200 characters',
            'is_unique' => 'Attendance type already exists'
        ],
        'key_value' => [
            'required' => 'Key value is required',
            'min_length' => 'Key value must be at least 1 character long',
            'max_length' => 'Key value cannot exceed 200 characters',
            'is_unique' => 'Key value already exists'
        ]
    ];

    protected $searchableColumns = ['type', 'key_value'];
    protected $orderableColumns = ['id', 'type', 'key_value', 'is_active'];

    /**
     * Get active attendance types
     */
    public function getActive()
    {
        return $this->where('is_active', 'yes')
                   ->orderBy('type', 'ASC')
                   ->findAll();
    }

    /**
     * Get attendance types for dropdown
     */
    public function getForDropdown()
    {
        $types = $this->getActive();
        
        $dropdown = [];
        foreach ($types as $type) {
            $dropdown[$type['id']] = $type['type'];
        }
        
        return $dropdown;
    }

    /**
     * Get attendance types for QR code
     */
    public function getForQRCode()
    {
        return $this->where('is_active', 'yes')
                   ->where('for_qr_attendance', 1)
                   ->orderBy('type', 'ASC')
                   ->findAll();
    }

    /**
     * Get attendance type by key value
     */
    public function getByKeyValue($keyValue)
    {
        return $this->where('key_value', $keyValue)
                   ->where('is_active', 'yes')
                   ->first();
    }

    /**
     * Get attendance type statistics
     */
    public function getStatistics()
    {
        $stats = [];
        
        // Total types
        $stats['total'] = $this->countAllResults();
        
        // Active types
        $stats['active'] = $this->where('is_active', 'yes')->countAllResults();
        
        // QR enabled types
        $stats['qr_enabled'] = $this->where('for_qr_attendance', 1)
                                   ->where('is_active', 'yes')
                                   ->countAllResults();
        
        // Usage statistics
        $stats['usage'] = $this->db->table($this->table)
                                  ->select('staff_attendance_type.type, COUNT(staff_attendance.id) as usage_count')
                                  ->join('staff_attendance', 'staff_attendance_type.id = staff_attendance.staff_attendance_type_id', 'left')
                                  ->where('staff_attendance_type.is_active', 'yes')
                                  ->groupBy('staff_attendance_type.id')
                                  ->orderBy('usage_count', 'DESC')
                                  ->get()
                                  ->getResultArray();
        
        return $stats;
    }

    /**
     * Create attendance type with validation
     */
    public function createAttendanceType($data)
    {
        // Set defaults
        $data['is_active'] = $data['is_active'] ?? 'yes';
        $data['for_qr_attendance'] = $data['for_qr_attendance'] ?? 1;

        if ($this->insert($data)) {
            return [
                'success' => true,
                'message' => 'Attendance type created successfully',
                'data' => $this->find($this->getInsertID())
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to create attendance type',
            'errors' => $this->errors()
        ];
    }

    /**
     * Update attendance type with validation
     */
    public function updateAttendanceType($id, $data)
    {
        if ($this->update($id, $data)) {
            return [
                'success' => true,
                'message' => 'Attendance type updated successfully',
                'data' => $this->find($id)
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to update attendance type',
            'errors' => $this->errors()
        ];
    }

    /**
     * Delete attendance type with validation
     */
    public function deleteAttendanceType($id)
    {
        // Check if attendance type is being used
        $usageCount = $this->db->table('staff_attendance')
                              ->where('staff_attendance_type_id', $id)
                              ->countAllResults();

        if ($usageCount > 0) {
            return [
                'success' => false,
                'message' => 'Cannot delete attendance type. It is being used in attendance records.'
            ];
        }

        if ($this->delete($id)) {
            return [
                'success' => true,
                'message' => 'Attendance type deleted successfully'
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to delete attendance type'
        ];
    }

    /**
     * Get default attendance types for seeding
     */
    public function getDefaultTypes()
    {
        return [
            [
                'type' => 'Present',
                'key_value' => 'P',
                'is_active' => 'yes',
                'for_qr_attendance' => 1,
                'long_lang_name' => 'Present',
                'long_name_style' => 'color: green;'
            ],
            [
                'type' => 'Absent',
                'key_value' => 'A',
                'is_active' => 'yes',
                'for_qr_attendance' => 1,
                'long_lang_name' => 'Absent',
                'long_name_style' => 'color: red;'
            ],
            [
                'type' => 'Late',
                'key_value' => 'L',
                'is_active' => 'yes',
                'for_qr_attendance' => 1,
                'long_lang_name' => 'Late',
                'long_name_style' => 'color: orange;'
            ],
            [
                'type' => 'Half Day',
                'key_value' => 'H',
                'is_active' => 'yes',
                'for_qr_attendance' => 1,
                'long_lang_name' => 'Half Day',
                'long_name_style' => 'color: blue;'
            ],
            [
                'type' => 'Permission',
                'key_value' => 'PR',
                'is_active' => 'yes',
                'for_qr_attendance' => 1,
                'long_lang_name' => 'Permission',
                'long_name_style' => 'color: purple;'
            ],
            [
                'type' => 'Medical Leave',
                'key_value' => 'ML',
                'is_active' => 'yes',
                'for_qr_attendance' => 0,
                'long_lang_name' => 'Medical Leave',
                'long_name_style' => 'color: teal;'
            ],
            [
                'type' => 'Casual Leave',
                'key_value' => 'CL',
                'is_active' => 'yes',
                'for_qr_attendance' => 0,
                'long_lang_name' => 'Casual Leave',
                'long_name_style' => 'color: indigo;'
            ]
        ];
    }

    /**
     * Bulk create default attendance types
     */
    public function createDefaultTypes()
    {
        $defaultTypes = $this->getDefaultTypes();
        $createdCount = 0;

        foreach ($defaultTypes as $type) {
            // Check if type already exists
            $existing = $this->where('key_value', $type['key_value'])->first();
            
            if (!$existing) {
                if ($this->insert($type)) {
                    $createdCount++;
                }
            }
        }

        return [
            'success' => true,
            'created_count' => $createdCount,
            'message' => "Created {$createdCount} default attendance types"
        ];
    }

    /**
     * Get attendance type with usage count
     */
    public function getWithUsageCount()
    {
        $builder = $this->db->table($this->table);
        $builder->select('staff_attendance_type.*, COUNT(staff_attendance.id) as usage_count')
                ->join('staff_attendance', 'staff_attendance_type.id = staff_attendance.staff_attendance_type_id', 'left')
                ->groupBy('staff_attendance_type.id')
                ->orderBy('staff_attendance_type.type', 'ASC');

        return $builder->get()->getResultArray();
    }

    /**
     * Check if attendance type can be deleted
     */
    public function canDelete($id)
    {
        $usageCount = $this->db->table('staff_attendance')
                              ->where('staff_attendance_type_id', $id)
                              ->countAllResults();

        return $usageCount === 0;
    }
}
