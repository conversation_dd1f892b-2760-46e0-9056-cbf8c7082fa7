<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class EnhanceAcademicCalendarForEvents extends Migration
{
    public function up()
    {
        // Add new fields to academic_calendar table to support events
        $fields = [
            'event_type' => [
                'type' => 'ENUM',
                'constraint' => ['holiday', 'event'],
                'null' => false,
                'default' => 'holiday',
                'comment' => 'Type: holiday or event',
                'after' => 'holiday_type_id'
            ],
            'organizer_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
                'comment' => 'Event organizer user ID',
                'after' => 'created_by'
            ],
            'location' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'null' => true,
                'comment' => 'Event location',
                'after' => 'description'
            ],
            'start_time' => [
                'type' => 'TIME',
                'null' => true,
                'comment' => 'Event start time',
                'after' => 'start_date'
            ],
            'end_time' => [
                'type' => 'TIME',
                'null' => true,
                'comment' => 'Event end time',
                'after' => 'end_date'
            ],
            'priority' => [
                'type' => 'ENUM',
                'constraint' => ['low', 'medium', 'high', 'urgent'],
                'null' => false,
                'default' => 'medium',
                'comment' => 'Event priority level',
                'after' => 'applies_to'
            ],
            'external_link' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'null' => true,
                'comment' => 'External link related to event',
                'after' => 'class_ids'
            ],
            'attachments' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => 'Array of file attachments',
                'after' => 'external_link'
            ]
        ];
        
        $this->forge->addColumn('academic_calendar', $fields);

        // Add indexes for new fields
        $this->forge->addKey('academic_calendar', 'event_type');
        $this->forge->addKey('academic_calendar', 'organizer_id');
        $this->forge->addKey('academic_calendar', 'priority');

        // Add foreign key for organizer_id
        $this->forge->addForeignKey('academic_calendar', 'organizer_id', 'users', 'id', 'SET NULL', 'SET NULL');

        // Add new holiday types for events
        $this->addEventHolidayTypes();
    }

    public function down()
    {
        // Remove foreign key first
        $this->forge->dropForeignKey('academic_calendar', 'academic_calendar_organizer_id_foreign');
        
        // Remove added columns
        $this->forge->dropColumn('academic_calendar', [
            'event_type',
            'organizer_id', 
            'location',
            'start_time',
            'end_time',
            'priority',
            'external_link',
            'attachments'
        ]);
    }

    /**
     * Add holiday types that can be used for events
     */
    private function addEventHolidayTypes()
    {
        $db = \Config\Database::connect();
        
        $eventTypes = [
            [
                'name' => 'School Events',
                'description' => 'General school events and activities',
                'color' => '#28a745',
                'is_academic_break' => 0,
                'affects_attendance' => 0
            ],
            [
                'name' => 'Sports Events',
                'description' => 'Sports competitions and activities',
                'color' => '#fd7e14',
                'is_academic_break' => 0,
                'affects_attendance' => 0
            ],
            [
                'name' => 'Cultural Events',
                'description' => 'Cultural programs and celebrations',
                'color' => '#e83e8c',
                'is_academic_break' => 0,
                'affects_attendance' => 0
            ],
            [
                'name' => 'Parent Meetings',
                'description' => 'Parent-teacher meetings and conferences',
                'color' => '#6f42c1',
                'is_academic_break' => 0,
                'affects_attendance' => 0
            ],
            [
                'name' => 'Examinations',
                'description' => 'Exam schedules and related events',
                'color' => '#dc3545',
                'is_academic_break' => 1,
                'affects_attendance' => 1
            ],
            [
                'name' => 'Staff Meetings',
                'description' => 'Administrative and staff meetings',
                'color' => '#6c757d',
                'is_academic_break' => 0,
                'affects_attendance' => 0
            ],
            [
                'name' => 'Extracurricular',
                'description' => 'Clubs, societies, hobby activities',
                'color' => '#20c997',
                'is_academic_break' => 0,
                'affects_attendance' => 0
            ],
            [
                'name' => 'Announcements',
                'description' => 'Important announcements and notices',
                'color' => '#ffc107',
                'is_academic_break' => 0,
                'affects_attendance' => 0
            ]
        ];

        foreach ($eventTypes as $type) {
            // Check if type already exists
            $existing = $db->table('holiday_types')
                          ->where('name', $type['name'])
                          ->get()
                          ->getRowArray();
            
            if (!$existing) {
                $type['is_active'] = 'yes';
                $type['created_at'] = date('Y-m-d H:i:s');
                $type['updated_at'] = date('Y-m-d H:i:s');
                
                $db->table('holiday_types')->insert($type);
            }
        }
    }
}
