<?php

namespace App\Models;

use CodeIgniter\Model;

class SchoolEventsModel extends Model
{
    protected $table = 'school_events';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'session_id', 'category_id', 'title', 'description', 'event_type',
        'start_date', 'end_date', 'start_time', 'end_time', 'location',
        'organizer_id', 'target_audience', 'class_ids', 'user_ids',
        'max_participants', 'registration_required', 'registration_deadline',
        'approval_status', 'approved_by', 'approved_at', 'priority',
        'is_featured', 'attachments', 'external_link', 'reminder_sent',
        'is_active', 'created_by'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    protected $validationRules = [
        'session_id' => 'required|integer',
        'category_id' => 'required|integer',
        'title' => 'required|min_length[3]|max_length[200]',
        'event_type' => 'required|in_list[academic,extracurricular,administrative,sports,cultural,meeting,announcement]',
        'start_date' => 'required|valid_date',
        'end_date' => 'required|valid_date',
        'target_audience' => 'permit_empty|in_list[all,students,staff,parents,specific_classes,specific_users]',
        'approval_status' => 'permit_empty|in_list[pending,approved,rejected]',
        'priority' => 'permit_empty|in_list[low,medium,high,urgent]',
        'is_active' => 'permit_empty|in_list[yes,no]'
    ];

    protected $validationMessages = [
        'session_id' => [
            'required' => 'Academic session is required',
            'integer' => 'Invalid session ID'
        ],
        'category_id' => [
            'required' => 'Event category is required',
            'integer' => 'Invalid category ID'
        ],
        'title' => [
            'required' => 'Event title is required',
            'min_length' => 'Event title must be at least 3 characters long',
            'max_length' => 'Event title cannot exceed 200 characters'
        ],
        'start_date' => [
            'required' => 'Start date is required',
            'valid_date' => 'Please enter a valid start date'
        ],
        'end_date' => [
            'required' => 'End date is required',
            'valid_date' => 'Please enter a valid end date'
        ]
    ];

    /**
     * Get events with details
     */
    public function getEventsWithDetails($filters = [])
    {
        helper('session');
        
        $builder = $this->db->table($this->table);
        $builder->select('school_events.*, event_categories.name as category_name, 
                         event_categories.color, event_categories.icon, 
                         sessions.session as session_name,
                         organizer.firstname as organizer_firstname,
                         organizer.lastname as organizer_lastname,
                         approver.firstname as approver_firstname,
                         approver.lastname as approver_lastname,
                         creator.firstname as creator_firstname,
                         creator.lastname as creator_lastname')
                ->join('event_categories', 'school_events.category_id = event_categories.id')
                ->join('sessions', 'school_events.session_id = sessions.id')
                ->join('users as organizer', 'school_events.organizer_id = organizer.id', 'left')
                ->join('users as approver', 'school_events.approved_by = approver.id', 'left')
                ->join('users as creator', 'school_events.created_by = creator.id', 'left')
                ->where('school_events.is_active', 'yes');

        // Apply session filter
        $sessionId = $filters['session_id'] ?? session()->get('current_session_id') ?? get_current_session_id();
        if ($sessionId) {
            $builder->where('school_events.session_id', $sessionId);
        }

        // Apply date range filter
        if (!empty($filters['start_date'])) {
            $builder->where('school_events.end_date >=', $filters['start_date']);
        }
        if (!empty($filters['end_date'])) {
            $builder->where('school_events.start_date <=', $filters['end_date']);
        }

        // Apply category filter
        if (!empty($filters['category_id'])) {
            $builder->where('school_events.category_id', $filters['category_id']);
        }

        // Apply event type filter
        if (!empty($filters['event_type'])) {
            $builder->where('school_events.event_type', $filters['event_type']);
        }

        // Apply approval status filter
        if (!empty($filters['approval_status'])) {
            $builder->where('school_events.approval_status', $filters['approval_status']);
        }

        // Apply target audience filter
        if (!empty($filters['target_audience'])) {
            $builder->where('school_events.target_audience', $filters['target_audience']);
        }

        // Apply priority filter
        if (!empty($filters['priority'])) {
            $builder->where('school_events.priority', $filters['priority']);
        }

        // Apply featured filter
        if (isset($filters['is_featured'])) {
            $builder->where('school_events.is_featured', $filters['is_featured']);
        }

        $builder->orderBy('school_events.start_date', 'ASC')
                ->orderBy('school_events.start_time', 'ASC');

        return $builder->get()->getResultArray();
    }

    /**
     * Get calendar events for FullCalendar
     */
    public function getCalendarEvents($filters = [])
    {
        $events = $this->getEventsWithDetails($filters);
        
        $calendarEvents = [];
        foreach ($events as $event) {
            $calendarEvents[] = [
                'id' => 'event_' . $event['id'],
                'title' => $event['title'],
                'start' => $event['start_date'] . ($event['start_time'] ? 'T' . $event['start_time'] : ''),
                'end' => $event['end_date'] . ($event['end_time'] ? 'T' . $event['end_time'] : ''),
                'color' => $event['color'],
                'description' => $event['description'],
                'className' => 'event-' . $event['event_type'],
                'extendedProps' => [
                    'type' => 'event',
                    'event_id' => $event['id'],
                    'category' => $event['category_name'],
                    'event_type' => $event['event_type'],
                    'location' => $event['location'],
                    'organizer' => trim($event['organizer_firstname'] . ' ' . $event['organizer_lastname']),
                    'target_audience' => $event['target_audience'],
                    'priority' => $event['priority'],
                    'approval_status' => $event['approval_status'],
                    'registration_required' => $event['registration_required'],
                    'is_featured' => $event['is_featured'],
                    'session_name' => $event['session_name']
                ]
            ];
        }

        return $calendarEvents;
    }

    /**
     * Create new event
     */
    public function createEvent($data)
    {
        // Validate date range
        if (strtotime($data['start_date']) > strtotime($data['end_date'])) {
            return [
                'success' => false,
                'message' => 'End date must be after or equal to start date'
            ];
        }

        // Set defaults
        $data['is_active'] = $data['is_active'] ?? 'yes';
        $data['approval_status'] = $data['approval_status'] ?? 'approved';
        $data['priority'] = $data['priority'] ?? 'medium';
        $data['target_audience'] = $data['target_audience'] ?? 'all';
        $data['registration_required'] = $data['registration_required'] ?? 0;
        $data['is_featured'] = $data['is_featured'] ?? 0;
        $data['reminder_sent'] = 0;

        // Handle JSON fields
        if (isset($data['class_ids']) && is_array($data['class_ids'])) {
            $data['class_ids'] = json_encode($data['class_ids']);
        }
        if (isset($data['user_ids']) && is_array($data['user_ids'])) {
            $data['user_ids'] = json_encode($data['user_ids']);
        }
        if (isset($data['attachments']) && is_array($data['attachments'])) {
            $data['attachments'] = json_encode($data['attachments']);
        }

        if ($this->insert($data)) {
            return [
                'success' => true,
                'message' => 'Event created successfully',
                'data' => $this->find($this->getInsertID())
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to create event',
            'errors' => $this->errors()
        ];
    }

    /**
     * Update event
     */
    public function updateEvent($id, $data)
    {
        // Validate date range if dates are being updated
        if (isset($data['start_date']) && isset($data['end_date'])) {
            if (strtotime($data['start_date']) > strtotime($data['end_date'])) {
                return [
                    'success' => false,
                    'message' => 'End date must be after or equal to start date'
                ];
            }
        }

        // Handle JSON fields
        if (isset($data['class_ids']) && is_array($data['class_ids'])) {
            $data['class_ids'] = json_encode($data['class_ids']);
        }
        if (isset($data['user_ids']) && is_array($data['user_ids'])) {
            $data['user_ids'] = json_encode($data['user_ids']);
        }
        if (isset($data['attachments']) && is_array($data['attachments'])) {
            $data['attachments'] = json_encode($data['attachments']);
        }

        if ($this->update($id, $data)) {
            return [
                'success' => true,
                'message' => 'Event updated successfully',
                'data' => $this->find($id)
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to update event',
            'errors' => $this->errors()
        ];
    }

    /**
     * Approve event
     */
    public function approveEvent($id, $approvedBy)
    {
        $data = [
            'approval_status' => 'approved',
            'approved_by' => $approvedBy,
            'approved_at' => date('Y-m-d H:i:s')
        ];

        if ($this->update($id, $data)) {
            return [
                'success' => true,
                'message' => 'Event approved successfully'
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to approve event'
        ];
    }

    /**
     * Reject event
     */
    public function rejectEvent($id, $approvedBy)
    {
        $data = [
            'approval_status' => 'rejected',
            'approved_by' => $approvedBy,
            'approved_at' => date('Y-m-d H:i:s')
        ];

        if ($this->update($id, $data)) {
            return [
                'success' => true,
                'message' => 'Event rejected successfully'
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to reject event'
        ];
    }

    /**
     * Get upcoming events
     */
    public function getUpcomingEvents($limit = 5, $sessionId = null)
    {
        helper('session');

        if (!$sessionId) {
            $sessionId = get_current_session_id();
        }

        $today = date('Y-m-d');

        $events = $this->getEventsWithDetails([
            'session_id' => $sessionId,
            'start_date' => $today,
            'approval_status' => 'approved'
        ]);

        // Apply limit
        return array_slice($events, 0, $limit);
    }

    /**
     * Get featured events
     */
    public function getFeaturedEvents($sessionId = null)
    {
        helper('session');

        if (!$sessionId) {
            $sessionId = get_current_session_id();
        }

        return $this->getEventsWithDetails([
            'session_id' => $sessionId,
            'is_featured' => 1,
            'approval_status' => 'approved'
        ]);
    }

    /**
     * Get events requiring approval
     */
    public function getEventsRequiringApproval($sessionId = null)
    {
        helper('session');

        if (!$sessionId) {
            $sessionId = get_current_session_id();
        }

        return $this->getEventsWithDetails([
            'session_id' => $sessionId,
            'approval_status' => 'pending'
        ]);
    }

    /**
     * Get event statistics
     */
    public function getStatistics($sessionId = null)
    {
        helper('session');

        if (!$sessionId) {
            $sessionId = get_current_session_id();
        }

        $totalEvents = $this->where('session_id', $sessionId)
                           ->where('is_active', 'yes')
                           ->countAllResults();

        $approvedEvents = $this->where('session_id', $sessionId)
                              ->where('approval_status', 'approved')
                              ->where('is_active', 'yes')
                              ->countAllResults();

        $pendingEvents = $this->where('session_id', $sessionId)
                             ->where('approval_status', 'pending')
                             ->where('is_active', 'yes')
                             ->countAllResults();

        $featuredEvents = $this->where('session_id', $sessionId)
                              ->where('is_featured', 1)
                              ->where('is_active', 'yes')
                              ->countAllResults();

        $upcomingEvents = $this->where('session_id', $sessionId)
                              ->where('start_date >=', date('Y-m-d'))
                              ->where('approval_status', 'approved')
                              ->where('is_active', 'yes')
                              ->countAllResults();

        return [
            'total_events' => $totalEvents,
            'approved_events' => $approvedEvents,
            'pending_events' => $pendingEvents,
            'featured_events' => $featuredEvents,
            'upcoming_events' => $upcomingEvents
        ];
    }
}
