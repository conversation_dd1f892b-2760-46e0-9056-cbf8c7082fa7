<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateStaffLeaveSystem extends Migration
{
    public function up()
    {
        // Create staff_leave_types table
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true
            ],
            'type' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'null' => false,
                'comment' => 'Leave type name'
            ],
            'max_leave' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => false,
                'default' => 0,
                'comment' => 'Maximum leaves allowed per year'
            ],
            'is_active' => [
                'type' => 'VARCHAR',
                'constraint' => 3,
                'null' => false,
                'default' => 'yes'
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true
            ]
        ]);

        $this->forge->addPrimaryKey('id');
        $this->forge->addKey('type');
        $this->forge->addKey('is_active');
        $this->forge->createTable('staff_leave_types');

        // Create staff_leave_request table
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true
            ],
            'staff_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => false,
                'comment' => 'Staff member ID'
            ],
            'leave_type_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
                'comment' => 'Leave type ID'
            ],
            'apply_date' => [
                'type' => 'DATE',
                'null' => false,
                'comment' => 'Date when leave was applied'
            ],
            'leave_from' => [
                'type' => 'DATE',
                'null' => false,
                'comment' => 'Leave start date'
            ],
            'leave_to' => [
                'type' => 'DATE',
                'null' => false,
                'comment' => 'Leave end date'
            ],
            'leave_days' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => false,
                'comment' => 'Number of leave days'
            ],
            'employee_remark' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Employee remarks/reason for leave'
            ],
            'admin_remark' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Admin remarks'
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['pending', 'approved', 'disapproved'],
                'null' => false,
                'default' => 'pending',
                'comment' => 'Leave request status'
            ],
            'approved_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
                'comment' => 'User who approved/disapproved'
            ],
            'approved_date' => [
                'type' => 'DATETIME',
                'null' => true,
                'comment' => 'Date when approved/disapproved'
            ],
            'document' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'null' => true,
                'comment' => 'Supporting document file path'
            ],
            'is_active' => [
                'type' => 'VARCHAR',
                'constraint' => 3,
                'null' => false,
                'default' => 'yes'
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true
            ]
        ]);

        $this->forge->addPrimaryKey('id');
        $this->forge->addKey('staff_id');
        $this->forge->addKey('leave_type_id');
        $this->forge->addKey('status');
        $this->forge->addKey(['leave_from', 'leave_to']);
        
        $this->forge->addForeignKey('staff_id', 'staff', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('leave_type_id', 'staff_leave_types', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('approved_by', 'users', 'id', 'SET NULL', 'SET NULL');
        
        $this->forge->createTable('staff_leave_request');

        // Insert default leave types
        $this->insertDefaultLeaveTypes();
    }

    public function down()
    {
        $this->forge->dropTable('staff_leave_request');
        $this->forge->dropTable('staff_leave_types');
    }

    /**
     * Insert default leave types
     */
    private function insertDefaultLeaveTypes()
    {
        $db = \Config\Database::connect();
        
        $leaveTypes = [
            [
                'type' => 'Casual Leave',
                'max_leave' => 12,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'type' => 'Medical Leave',
                'max_leave' => 15,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'type' => 'Earned Leave',
                'max_leave' => 21,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'type' => 'Maternity Leave',
                'max_leave' => 180,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'type' => 'Paternity Leave',
                'max_leave' => 15,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'type' => 'Emergency Leave',
                'max_leave' => 5,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'type' => 'Study Leave',
                'max_leave' => 30,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        foreach ($leaveTypes as $leaveType) {
            $db->table('staff_leave_types')->insert($leaveType);
        }
    }
}
